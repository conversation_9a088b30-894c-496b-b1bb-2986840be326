<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@id/rl_simple_guide_big_distance" style="@style/DiffNaviLlSimpleGuideBigDistance"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:id="@id/iv_via" android:visibility="gone" android:layout_width="@dimen/tp_32" android:layout_height="@dimen/tp_38" android:layout_marginRight="@dimen/tp_22" android:src="@mipmap/icon_via" android:layout_centerVertical="true" android:layout_alignParentStart="true" />
    <TextView android:id="@id/tv_max_eta_distance" android:layout_centerVertical="true" android:layout_toEndOf="@id/iv_via" style="@style/DiffGuideTurnMaxTimeTextBigDistanceTv" />
    <TextView android:id="@id/tv_max_eta_distance_unit" android:layout_centerVertical="true" android:layout_toEndOf="@id/tv_max_eta_distance" style="@style/DiffGuideTurnMaxTimeTextBigDistanceTextUnitTv" />
    <TextView android:textColor="@color/diff_guide_tbt_distance_eta_color" android:id="@id/tv_max_eta_point" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/dot" android:layout_centerVertical="true" android:layout_toEndOf="@id/tv_max_eta_distance_unit" android:layout_marginHorizontal="@dimen/tp_4" />
    <TextView android:id="@id/tv_max_eta_time_hour" android:layout_centerVertical="true" android:layout_toEndOf="@id/tv_max_eta_point" style="@style/DiffNaviMaxEtaDistanceTv" />
    <TextView android:id="@id/tv_max_eta_time_hour_unit" android:text="@string/hour" android:layout_centerVertical="true" android:layout_toEndOf="@id/tv_max_eta_time_hour" style="@style/DiffGuideTurnMaxTimeTextBigDistanceTextTv" />
    <TextView android:id="@id/tv_max_eta_time_minute" android:layout_centerVertical="true" android:layout_toEndOf="@id/tv_max_eta_time_hour_unit" style="@style/DiffNaviMaxEtaDistanceTv" />
    <TextView android:id="@id/tv_max_eta_time_minute_unit" android:text="@string/minute_simple" android:layout_centerVertical="true" android:layout_toEndOf="@id/tv_max_eta_time_minute" style="@style/DiffGuideTurnMaxTimeTextBigDistanceTextTv" />
    <TextView android:id="@id/simple_guide_big_time_tv" android:layout_centerVertical="true" android:layout_toStartOf="@id/simple_guide_big_time_plus_tv" style="@style/DiffGuideTurnMaxArrivalTime" />
    <TextView android:id="@id/simple_guide_big_time_plus_tv" android:paddingTop="@dimen/tp_4" android:layout_alignTop="@id/simple_guide_big_arrive_tv" android:paddingEnd="@dimen/tp_5" android:layout_toStartOf="@id/simple_guide_big_arrive_tv" style="@style/DiffNaviSimpleGuideArriveTimePlus" />
    <TextView android:id="@id/simple_guide_big_arrive_tv" android:text="@string/route_guide_arrive" android:layout_centerVertical="true" android:layout_alignParentEnd="true" style="@style/DiffNaviSimpleGuideArriveText" />
</RelativeLayout>