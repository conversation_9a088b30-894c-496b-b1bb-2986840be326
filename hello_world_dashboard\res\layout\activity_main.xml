<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/hello_world"
        android:textSize="24sp"
        android:layout_gravity="center"
        android:layout_marginBottom="32dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/dashboard_test"
        android:textSize="18sp"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/btn_full_navi"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/full_navi"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/btn_simple_navi"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/simple_navi"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/btn_exit_navi"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/exit_navi"
        android:layout_marginBottom="8dp" />

</LinearLayout>
