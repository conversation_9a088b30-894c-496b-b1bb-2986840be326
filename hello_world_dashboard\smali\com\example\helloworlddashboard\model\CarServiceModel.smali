.class public Lcom/example/helloworlddashboard/model/CarServiceModel;
.super Lcom/example/helloworlddashboard/model/BaseServiceModel;
.source "CarServiceModel.java"

# interfaces
.implements Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;
.implements Lcom/example/helloworlddashboard/utils/TroubleshootingHelp$ThemeDetectionCallback;
.implements Lcom/example/helloworlddashboard/model/InteractiveModel$ICarServiceProtocol;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;
    }
.end annotation


# static fields
.field private static final ACTION_INSTRUMENT_NAVIGATION:Ljava/lang/String; = "com.example.helloworlddashboard.ENTERS_THE_INSTRUMENT_NAVIGATION"

.field private static final ACTION_SCREEN_CAST:Ljava/lang/String; = "action_screen_cast"

.field private static final ACTION_SIMPLE_NAVI:Ljava/lang/String; = "action_simple_navi"

.field private static final TAG:Ljava/lang/String; = "SignTestHelper"


# instance fields
.field private final NEED_SCREEN_CAST_REST_MODE:Ljava/lang/String;

.field private final broadcastReceiver:Landroid/content/BroadcastReceiver;

.field private volatile canFusion:Z

.field private driverType:I

.field private final fullNaviChangeTask:Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;

.field private heartbeatHelp:Lcom/example/helloworlddashboard/utils/HeartbeatHelp;

.field private final initHeartbeatTask:Ljava/lang/Runnable;

.field private volatile interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

.field private final ipScreenReqClearTask:Ljava/lang/Runnable;

.field private lastAvmValue:I

.field lastFlyingScreenTime:J

.field private lastReceiverTime:J

.field private lastRequestValue:I

.field private lastRequestValueTime:J

.field private mCurrTheme:I

.field private final mReceiveNaviChangeTask:Ljava/lang/Runnable;

.field private simpleNaviRunning:Z

.field private testMockHelp:Lcom/example/helloworlddashboard/utils/TestMockHelp;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/example/helloworlddashboard/callback/CarServiceCallback;)V
    .locals 2

    .line 126
    invoke-direct {p0, p1, p2}, Lcom/example/helloworlddashboard/model/BaseServiceModel;-><init>(Landroid/content/Context;Lcom/example/helloworlddashboard/callback/CarServiceCallback;)V

    const/4 p1, -0x1

    .line 69
    iput p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    const/4 p1, 0x0

    .line 73
    iput-boolean p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->canFusion:Z

    const-wide/16 v0, 0x0

    .line 82
    iput-wide v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastReceiverTime:J

    .line 87
    iput-wide v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastFlyingScreenTime:J

    .line 89
    iput-boolean p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->simpleNaviRunning:Z

    .line 93
    new-instance p1, Lcom/example/helloworlddashboard/model/-$$Lambda$CarServiceModel$xTUMGqCg1PKqeoGd9LY-enC6-08;

    invoke-direct {p1, p0}, Lcom/example/helloworlddashboard/model/-$$Lambda$CarServiceModel$xTUMGqCg1PKqeoGd9LY-enC6-08;-><init>(Lcom/example/helloworlddashboard/model/CarServiceModel;)V

    iput-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->initHeartbeatTask:Ljava/lang/Runnable;

    .line 110
    new-instance p1, Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;

    const/4 p2, 0x0

    invoke-direct {p1, p0, p2}, Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;-><init>(Lcom/example/helloworlddashboard/model/CarServiceModel;Lcom/example/helloworlddashboard/model/CarServiceModel$1;)V

    iput-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->fullNaviChangeTask:Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;

    .line 123
    new-instance p1, Lcom/example/helloworlddashboard/model/-$$Lambda$CarServiceModel$lqsasHVxHuo-w_1UX0IZ6PpLyg8;

    invoke-direct {p1, p0}, Lcom/example/helloworlddashboard/model/-$$Lambda$CarServiceModel$lqsasHVxHuo-w_1UX0IZ6PpLyg8;-><init>(Lcom/example/helloworlddashboard/model/CarServiceModel;)V

    iput-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->ipScreenReqClearTask:Ljava/lang/Runnable;

    const-string p1, "need_screen_cast_rest_mode"

    .line 651
    iput-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->NEED_SCREEN_CAST_REST_MODE:Ljava/lang/String;

    .line 1099
    new-instance p1, Lcom/example/helloworlddashboard/model/CarServiceModel$1;

    invoke-direct {p1, p0}, Lcom/example/helloworlddashboard/model/CarServiceModel$1;-><init>(Lcom/example/helloworlddashboard/model/CarServiceModel;)V

    iput-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->broadcastReceiver:Landroid/content/BroadcastReceiver;

    .line 1231
    new-instance p1, Lcom/example/helloworlddashboard/model/-$$Lambda$CarServiceModel$qMd_HSoggZ3lwKV0PUBGOLQG37w;

    invoke-direct {p1, p0}, Lcom/example/helloworlddashboard/model/-$$Lambda$CarServiceModel$qMd_HSoggZ3lwKV0PUBGOLQG37w;-><init>(Lcom/example/helloworlddashboard/model/CarServiceModel;)V

    iput-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mReceiveNaviChangeTask:Ljava/lang/Runnable;

    return-void
.end method

.method static synthetic access$200(Lcom/example/helloworlddashboard/model/CarServiceModel;Landroid/content/Intent;)V
    .locals 0

    .line 43
    invoke-direct {p0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->parseVoiceAction(Landroid/content/Intent;)V

    return-void
.end method

.method static synthetic access$300(Lcom/example/helloworlddashboard/model/CarServiceModel;I)V
    .locals 0

    .line 43
    invoke-direct {p0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->showSleep(I)V

    return-void
.end method

.method static synthetic access$400(Lcom/example/helloworlddashboard/model/CarServiceModel;)J
    .locals 2

    .line 43
    iget-wide v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastReceiverTime:J

    return-wide v0
.end method

.method static synthetic access$402(Lcom/example/helloworlddashboard/model/CarServiceModel;J)J
    .locals 0

    .line 43
    iput-wide p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastReceiverTime:J

    return-wide p1
.end method

.method static synthetic access$500(Lcom/example/helloworlddashboard/model/CarServiceModel;)Lcom/example/helloworlddashboard/model/IInteractiveProtocol;
    .locals 0

    .line 43
    iget-object p0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    return-object p0
.end method

.method static synthetic access$600(Lcom/example/helloworlddashboard/model/CarServiceModel;Z)V
    .locals 0

    .line 43
    invoke-direct {p0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->screenFullNavi(Z)V

    return-void
.end method

.method static synthetic access$700(Lcom/example/helloworlddashboard/model/CarServiceModel;)Lcom/example/helloworlddashboard/utils/TestMockHelp;
    .locals 0

    .line 43
    iget-object p0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->testMockHelp:Lcom/example/helloworlddashboard/utils/TestMockHelp;

    return-object p0
.end method

.method static synthetic access$800(Lcom/example/helloworlddashboard/model/CarServiceModel;Z)V
    .locals 0

    .line 43
    invoke-direct {p0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->fullNaviStateChange(Z)V

    return-void
.end method

.method private acknowledge(III)V
    .locals 3

    .line 696
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    const/4 v1, 0x3

    new-array v1, v1, [I

    const/4 v2, 0x0

    aput p1, v1, v2

    const/4 p1, 0x1

    aput p3, v1, p1

    const/4 p1, 0x2

    aput p2, v1, p1

    const p1, 0x2141f433

    invoke-static {v0, p1, v2, v1}, Lcom/example/helloworlddashboard/utils/DataUtil;->setCabinIntArrayValue(Landroid/car/hardware/cabin/CarCabinManager;II[I)V

    return-void
.end method

.method private aiLightStsParse(I)V
    .locals 3

    .line 755
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "ID_BODY_MUSICLIGHTMODESELECT_STS value:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "SignTestHelper"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 766
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->getBasePresentation()Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    move-result-object v0

    if-eqz v0, :cond_4

    .line 767
    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->getCurrentAnimationType()I

    move-result v1

    const/16 v2, 0x16

    if-eq v1, v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x4

    if-ne p1, v1, :cond_1

    const/4 p1, 0x0

    .line 771
    invoke-virtual {v0, p1}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->playOrPauseLightAnim(Z)V

    goto :goto_0

    :cond_1
    const/4 v1, 0x1

    if-eq p1, v1, :cond_2

    const/4 v2, 0x2

    if-eq p1, v2, :cond_2

    const/4 v2, 0x3

    if-ne p1, v2, :cond_4

    .line 773
    :cond_2
    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->isPauseLightAnim()Z

    move-result p1

    if-eqz p1, :cond_3

    .line 774
    invoke-virtual {v0, v1}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->playOrPauseLightAnim(Z)V

    .line 776
    :cond_3
    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->playAiLight()V

    :cond_4
    :goto_0
    return-void
.end method

.method private enterTheme()V
    .locals 4

    .line 421
    invoke-static {}, Lcom/example/helloworlddashboard/utils/Logcat;->d()V

    .line 422
    iget v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->driverType:I

    const/4 v1, 0x6

    if-eq v1, v0, :cond_1

    const/16 v2, 0xa

    if-ne v2, v0, :cond_0

    goto :goto_0

    .line 426
    :cond_0
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissMap()V

    .line 427
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissSdPresentation()V

    goto :goto_2

    .line 424
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    iget v2, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->driverType:I

    const/4 v3, 0x0

    if-ne v1, v2, :cond_2

    const/4 v1, 0x1

    goto :goto_1

    :cond_2
    move v1, v3

    :goto_1
    const/4 v2, 0x0

    invoke-static {v0, v3, v1, v2}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showMapMode(Landroid/content/Context;IZLcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    .line 429
    :goto_2
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissPlayAim()V

    return-void
.end method

.method private exitCastScreen()V
    .locals 2

    const-string v0, "SignTestHelper"

    const-string v1, "dismissSdPresentation  false:"

    .line 158
    invoke-static {v0, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v0, 0x0

    .line 159
    invoke-direct {p0, v0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->exitCastScreen(Z)V

    return-void
.end method

.method private exitCastScreen(Z)V
    .locals 2

    .line 168
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "lastRequestValue:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastRequestValue:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " , exitAll:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "SignTestHelper"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 169
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissSdPresentation()V

    .line 170
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissMap()V

    .line 171
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissKeepOn()V

    if-eqz p1, :cond_0

    .line 173
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissPlayAim()V

    goto :goto_0

    :cond_0
    const/16 p1, 0x1c

    .line 175
    iget v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastRequestValue:I

    if-eq p1, v0, :cond_1

    const/16 p1, 0x10

    if-eq p1, v0, :cond_1

    const/16 p1, 0x1e

    if-ne p1, v0, :cond_3

    :cond_1
    const/16 p1, 0x16

    .line 178
    invoke-static {p1}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isSpecialAnimShowing(I)Z

    move-result p1

    if-nez p1, :cond_2

    const/16 p1, 0x15

    .line 179
    invoke-static {p1}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isSpecialAnimShowing(I)Z

    move-result p1

    if-nez p1, :cond_2

    const/16 p1, 0x17

    .line 180
    invoke-static {p1}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isSpecialAnimShowing(I)Z

    move-result p1

    if-eqz p1, :cond_3

    .line 181
    :cond_2
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissPlayAim()V

    .line 184
    :cond_3
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->exitSimpleNavi()V

    :goto_0
    const/4 p1, -0x1

    .line 187
    invoke-direct {p0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateMeterState(I)V

    return-void
.end method

.method private exitSimpleNavi()V
    .locals 1

    .line 685
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-interface {v0}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->exitSimpleNavi()V

    return-void
.end method

.method private exitSpecialScreen()V
    .locals 2

    .line 663
    iget-boolean v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->simpleNaviRunning:Z

    if-eqz v0, :cond_0

    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->isPowerOn()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 664
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "simpleNaviRunning= "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-boolean v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->simpleNaviRunning:Z

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 665
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->showSimpleNaviPresentation()V

    :cond_0
    return-void
.end method

.method private fullNaviStateChange(Z)V
    .locals 2

    .line 467
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "fullNaviStateChange isEnterOrExit:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " , interactiveProtocol\uff1a"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "SignTestHelper"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 468
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    if-eqz v0, :cond_1

    .line 469
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-interface {v0, p1}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->meterNaviStateChange(Z)V

    :cond_1
    return-void
.end method

.method private getCurrTheme()I
    .locals 2

    .line 726
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "getCurrTheme success. themeValue:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "SignTestHelper"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 727
    iget v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    return v0
.end method

.method private getCurrentThemeFromSet()I
    .locals 5

    .line 208
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    const v1, 0x2140f404

    const/4 v2, 0x0

    invoke-static {v0, v1, v2}, Lcom/example/helloworlddashboard/utils/DataUtil;->getCarCabinIntValue(Landroid/car/hardware/cabin/CarCabinManager;II)I

    move-result v0

    const/4 v1, 0x5

    const/4 v2, 0x3

    const/4 v3, 0x2

    const/4 v4, 0x1

    if-eq v0, v4, :cond_2

    if-eq v0, v3, :cond_1

    if-eq v0, v2, :cond_0

    const/4 v2, 0x4

    if-eq v0, v2, :cond_2

    if-eq v0, v1, :cond_3

    goto :goto_0

    :cond_0
    move v1, v2

    goto :goto_1

    :cond_1
    move v1, v3

    goto :goto_1

    :cond_2
    :goto_0
    move v1, v4

    .line 231
    :cond_3
    :goto_1
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getCurrentThemeFromSet themeSetValue:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, " , themeValue:"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v2, "SignTestHelper"

    invoke-static {v2, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v1
.end method

.method private getCurrentThemeStatus()I
    .locals 1

    const/4 v0, 0x0

    .line 239
    invoke-direct {p0, v0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->getCurrentThemeStatus(Z)I

    move-result v0

    return v0
.end method

.method private getCurrentThemeStatus(Z)I
    .locals 2

    if-eqz p1, :cond_0

    .line 248
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->getCurrentThemeFromSet()I

    move-result p1

    goto :goto_0

    .line 250
    :cond_0
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    const v0, 0x2140f3e6

    const/4 v1, 0x0

    invoke-static {p1, v0, v1}, Lcom/example/helloworlddashboard/utils/DataUtil;->getCarCabinIntValue(Landroid/car/hardware/cabin/CarCabinManager;II)I

    move-result p1

    :goto_0
    return p1
.end method

.method private ifNeedScreenCastRestMode()Z
    .locals 4

    .line 657
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v0

    const-string v1, "need_screen_cast_rest_mode"

    const/4 v2, 0x0

    invoke-static {v0, v1, v2}, Landroid/provider/Settings$System;->getInt(Landroid/content/ContentResolver;Ljava/lang/String;I)I

    move-result v0

    .line 658
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "needScreenCastRest :"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;)V

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    move v2, v1

    :cond_0
    return v2
.end method

.method private imformationTransmissionRequest([Ljava/lang/Integer;)V
    .locals 9

    if-eqz p1, :cond_19

    .line 505
    array-length v0, p1

    const/4 v1, 0x2

    if-eq v0, v1, :cond_0

    goto/16 :goto_6

    :cond_0
    const/4 v0, 0x0

    .line 508
    aget-object v2, p1, v0

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result v2

    const/4 v3, 0x1

    .line 509
    aget-object p1, p1, v3

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    if-nez v2, :cond_1

    .line 510
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->isPowerOn()Z

    move-result v4

    if-eqz v4, :cond_1

    .line 511
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateKeepOnMode()V

    return-void

    .line 514
    :cond_1
    iget v4, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastRequestValue:I

    if-ne v4, v2, :cond_2

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v4

    iget-wide v6, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastRequestValueTime:J

    sub-long/2addr v4, v6

    const-wide/16 v6, 0x3e8

    cmp-long v4, v4, v6

    if-gez v4, :cond_2

    .line 515
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "imformationTransmissionRequest May send values continuously, mask out. lastRequestValue:"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastRequestValue:I

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "SignTestHelper"

    invoke-static {v0, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 518
    :cond_2
    iput v2, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastRequestValue:I

    .line 519
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v4

    iput-wide v4, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastRequestValueTime:J

    const/16 v4, 0x10

    const/4 v5, -0x1

    const/4 v6, 0x0

    if-eq v2, v4, :cond_16

    const/16 v7, 0x11

    if-ne v2, v7, :cond_3

    goto/16 :goto_5

    :cond_3
    const/16 v4, 0x1c

    const/16 v7, 0x16

    if-eq v2, v4, :cond_13

    const/16 v8, 0x1d

    if-ne v2, v8, :cond_4

    goto/16 :goto_4

    :cond_4
    const/16 v4, 0x14

    if-eq v2, v4, :cond_12

    const/16 v8, 0x15

    if-ne v2, v8, :cond_5

    goto/16 :goto_3

    :cond_5
    if-eq v2, v7, :cond_c

    const/16 v8, 0x17

    if-ne v2, v8, :cond_6

    goto :goto_2

    :cond_6
    const/4 v4, 0x4

    if-eq v2, v4, :cond_b

    const/16 v4, 0x12

    if-ne v2, v4, :cond_7

    goto :goto_1

    :cond_7
    const/16 v4, 0x18

    if-eq v2, v4, :cond_a

    const/16 v4, 0x19

    if-ne v2, v4, :cond_8

    goto :goto_0

    :cond_8
    const/4 v3, 0x5

    if-ne v2, v3, :cond_9

    .line 625
    iput-boolean v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->simpleNaviRunning:Z

    .line 627
    invoke-direct {p0, v1, v2, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->acknowledge(III)V

    .line 628
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->isLightShowing()Z

    move-result p1

    invoke-direct {p0, v0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->stopSimpleNaviPresentation(ZZ)V

    goto/16 :goto_6

    .line 647
    :cond_9
    invoke-direct {p0, v2, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->themeSwitch(II)V

    goto/16 :goto_6

    .line 616
    :cond_a
    :goto_0
    iput-boolean v3, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->simpleNaviRunning:Z

    .line 617
    invoke-direct {p0, v1, v2, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->acknowledge(III)V

    .line 620
    invoke-direct {p0, v2}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateKeepOnMode(I)V

    .line 623
    invoke-virtual {p0, v2}, Lcom/example/helloworlddashboard/model/CarServiceModel;->enterMapScreen(I)V

    goto/16 :goto_6

    .line 607
    :cond_b
    :goto_1
    invoke-direct {p0, v1, v2, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->acknowledge(III)V

    .line 608
    invoke-direct {p0, v2}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateKeepOnMode(I)V

    .line 611
    iput-boolean v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->simpleNaviRunning:Z

    .line 613
    invoke-virtual {p0, v2}, Lcom/example/helloworlddashboard/model/CarServiceModel;->enterMapScreen(I)V

    goto/16 :goto_6

    :cond_c
    :goto_2
    if-ne v2, v7, :cond_10

    .line 571
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->ifNeedScreenCastRestMode()Z

    move-result v7

    if-nez v7, :cond_d

    const-string v0, "need not screen cast rest mode"

    .line 572
    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 573
    invoke-direct {p0, v3, v2, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->acknowledge(III)V

    return-void

    .line 578
    :cond_d
    invoke-direct {p0, v1, v2, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->acknowledge(III)V

    .line 579
    invoke-direct {p0, v2}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateKeepOnMode(I)V

    .line 580
    iget-boolean p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->simpleNaviRunning:Z

    if-eqz p1, :cond_e

    .line 581
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->isLightShowing()Z

    move-result p1

    invoke-direct {p0, v0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->stopSimpleNaviPresentation(ZZ)V

    .line 584
    :cond_e
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isFitnessAnimShowing()Z

    move-result p1

    if-eqz p1, :cond_f

    .line 585
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissFitnessPresentation()V

    .line 587
    :cond_f
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    invoke-static {p1, v0, v4, v6}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showOneDisplayRepeatMedia(Landroid/content/Context;Landroid/car/hardware/cabin/CarCabinManager;ILcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    .line 588
    invoke-direct {p0, v5}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateMeterState(I)V

    goto/16 :goto_6

    .line 590
    :cond_10
    invoke-direct {p0, v1, v2, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->acknowledge(III)V

    .line 591
    invoke-direct {p0, v2}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateKeepOnMode(I)V

    .line 594
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->exitSpecialScreen()V

    .line 596
    invoke-static {v4}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isSpecialAnimShowing(I)Z

    move-result p1

    if-eqz p1, :cond_11

    .line 597
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissPlayAim()V

    .line 603
    :cond_11
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-interface {p1}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->runTopAppChangTask()V

    goto :goto_6

    .line 567
    :cond_12
    :goto_3
    invoke-direct {p0, v1, v2, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->acknowledge(III)V

    goto :goto_6

    .line 550
    :cond_13
    :goto_4
    invoke-direct {p0, v1, v2, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->acknowledge(III)V

    if-ne v2, v4, :cond_15

    .line 552
    iget-boolean p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->simpleNaviRunning:Z

    if-eqz p1, :cond_14

    .line 553
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->isLightShowing()Z

    move-result p1

    invoke-direct {p0, v0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->stopSimpleNaviPresentation(ZZ)V

    .line 555
    :cond_14
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    invoke-static {p1, v0, v7, v6}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showOneDisplayRepeatMedia(Landroid/content/Context;Landroid/car/hardware/cabin/CarCabinManager;ILcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    .line 556
    invoke-direct {p0, v5}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateMeterState(I)V

    goto :goto_6

    .line 558
    :cond_15
    invoke-direct {p0, v1, v2, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->acknowledge(III)V

    .line 559
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->exitSpecialScreen()V

    .line 560
    invoke-static {v7}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isSpecialAnimShowing(I)Z

    move-result p1

    if-eqz p1, :cond_19

    .line 561
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissPlayAim()V

    goto :goto_6

    :cond_16
    :goto_5
    if-ne v2, v4, :cond_18

    .line 527
    invoke-direct {p0, v1, v2, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->acknowledge(III)V

    .line 528
    iget-boolean p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->simpleNaviRunning:Z

    if-eqz p1, :cond_17

    .line 529
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->isLightShowing()Z

    move-result p1

    invoke-direct {p0, v0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->stopSimpleNaviPresentation(ZZ)V

    .line 531
    :cond_17
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    invoke-static {p1, v6}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showOneDisplayFitness(Landroid/content/Context;Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    .line 532
    invoke-direct {p0, v5}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateMeterState(I)V

    goto :goto_6

    .line 535
    :cond_18
    invoke-direct {p0, v1, v2, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->acknowledge(III)V

    .line 536
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->exitSpecialScreen()V

    .line 537
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isFitnessAnimShowing()Z

    move-result p1

    if-eqz p1, :cond_19

    .line 538
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissFitnessPresentation()V

    :cond_19
    :goto_6
    return-void
.end method

.method private initHeartbeat()V
    .locals 5

    .line 133
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->initHeartbeatTask:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    .line 136
    monitor-enter p0

    .line 137
    :try_start_0
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    .line 138
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_1

    .line 141
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->appReturnToNormal()V

    const-string v1, "SignTestHelper"

    const-string v2, "appReturnToNormal"

    .line 142
    invoke-static {v1, v2}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 145
    :cond_1
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v1

    iget-object v2, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->initHeartbeatTask:Ljava/lang/Runnable;

    if-eqz v0, :cond_2

    const-wide/16 v3, 0xc8

    goto :goto_1

    :cond_2
    const-wide/16 v3, 0x2710

    :goto_1
    invoke-virtual {v1, v2, v3, v4}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    return-void

    :catchall_0
    move-exception v0

    .line 138
    :try_start_1
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v0
.end method

.method private isPowerOn()Z
    .locals 3

    .line 705
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    const v1, 0x2140f05e

    const/4 v2, 0x0

    invoke-static {v0, v1, v2}, Lcom/example/helloworlddashboard/utils/DataUtil;->getCabinIntValue(Landroid/car/hardware/cabin/CarCabinManager;II)I

    move-result v0

    const/4 v1, 0x4

    if-ne v0, v1, :cond_0

    const/4 v2, 0x1

    .line 706
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "isPowerOn:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "SignTestHelper"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v2
.end method

.method private parseVoiceAction(Landroid/content/Intent;)V
    .locals 4

    const-string v0, "key_type_display"

    const/4 v1, 0x0

    .line 1166
    invoke-virtual {p1, v0, v1}, Landroid/content/Intent;->getIntExtra(Ljava/lang/String;I)I

    move-result v0

    const-string v2, "key_switch"

    .line 1168
    invoke-virtual {p1, v2, v1}, Landroid/content/Intent;->getIntExtra(Ljava/lang/String;I)I

    move-result p1

    .line 1169
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "parseVoiceAction display:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, " , switchState:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const-string v3, "SignTestHelper"

    invoke-static {v3, v2}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v2, 0x1

    if-ne v2, v0, :cond_1

    if-ne v2, p1, :cond_0

    .line 1172
    invoke-direct {p0, v2}, Lcom/example/helloworlddashboard/model/CarServiceModel;->voiceFullNaviRequest(Z)V

    .line 1173
    invoke-direct {p0, v2}, Lcom/example/helloworlddashboard/model/CarServiceModel;->screenFullNavi(Z)V

    goto :goto_0

    :cond_0
    const/4 v0, 0x2

    if-ne v0, p1, :cond_1

    .line 1175
    invoke-direct {p0, v1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->voiceFullNaviRequest(Z)V

    .line 1176
    invoke-direct {p0, v1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->screenFullNavi(Z)V

    :cond_1
    :goto_0
    return-void
.end method

.method private receiveMeterFullNavi()V
    .locals 4

    .line 323
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mReceiveNaviChangeTask:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    .line 324
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mReceiveNaviChangeTask:Ljava/lang/Runnable;

    const-wide/16 v2, 0x3e8

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    return-void
.end method

.method private screenFullNavi(Z)V
    .locals 3

    .line 327
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->fullNaviChangeTask:Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    .line 329
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->fullNaviChangeTask:Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;

    invoke-virtual {v0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;->setMeterNaviRunning(Z)V

    .line 330
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object p1

    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->fullNaviChangeTask:Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;

    const-wide/16 v1, 0x15e

    invoke-virtual {p1, v0, v1, v2}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    return-void
.end method

.method private showSimpleNaviPresentation()V
    .locals 2

    .line 676
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-interface {v0}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->isNaviRunning()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    .line 677
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    invoke-static {v1}, Lcom/example/helloworlddashboard/utils/ThemeChangeHelp;->isNight(Landroid/content/Context;)Z

    move-result v1

    invoke-virtual {p0, v0, v1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->showOrHideSimpleNaviPresentation(ZZ)V

    :cond_0
    return-void
.end method

.method private showSleep(I)V
    .locals 3

    .line 1182
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "showSleep value= "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "SignTestHelper"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/16 v0, 0x16

    .line 1184
    invoke-direct {p0, v0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateKeepOnMode(I)V

    const/4 v1, 0x2

    const/4 v2, 0x0

    .line 1185
    invoke-direct {p0, v1, v0, v2}, Lcom/example/helloworlddashboard/model/CarServiceModel;->acknowledge(III)V

    const/16 v0, 0x14

    const/4 v1, 0x1

    if-ne p1, v1, :cond_0

    .line 1188
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    const/4 v2, 0x0

    invoke-static {p1, v1, v0, v2}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showOneDisplayRepeatMedia(Landroid/content/Context;Landroid/car/hardware/cabin/CarCabinManager;ILcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    const/4 p1, -0x1

    .line 1189
    invoke-direct {p0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateMeterState(I)V

    goto :goto_0

    .line 1192
    :cond_0
    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isSpecialAnimShowing(I)Z

    move-result p1

    if-eqz p1, :cond_1

    .line 1193
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissPlayAim()V

    .line 1195
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->exitCastScreen()V

    .line 1199
    :cond_1
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-interface {p1}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->upDataNaviShow()V

    :goto_0
    return-void
.end method

.method private stopSimpleNaviPresentation(ZZ)V
    .locals 0

    const-string p1, "stopSimpleNaviPresentation"

    .line 670
    invoke-static {p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;)V

    const/4 p1, 0x0

    .line 672
    invoke-virtual {p0, p1, p2}, Lcom/example/helloworlddashboard/model/CarServiceModel;->showOrHideSimpleNaviPresentation(ZZ)V

    return-void
.end method

.method private themeSwitch(II)V
    .locals 8

    .line 261
    invoke-static {p1}, Lcom/example/helloworlddashboard/utils/DataUtil;->requestValueToThemeValueAndDriveType(I)[I

    move-result-object v0

    const-string v1, "SignTestHelper"

    if-eqz v0, :cond_6

    .line 262
    array-length v2, v0

    const/4 v3, 0x2

    if-eq v2, v3, :cond_0

    goto/16 :goto_2

    .line 266
    :cond_0
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v2

    iget-object v4, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->fullNaviChangeTask:Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;

    invoke-virtual {v2, v4}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    .line 268
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->getCurrTheme()I

    move-result v2

    const/4 v4, 0x0

    .line 269
    aget v5, v0, v4

    iput v5, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->driverType:I

    const/4 v5, 0x1

    .line 270
    aget v0, v0, v5

    .line 271
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "themeSwitch requestValue:"

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v6

    const-string v7, " , currTheme:"

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v6, " , compareTheme:"

    invoke-virtual {v2, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v6, " driveType:"

    invoke-virtual {v2, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget v6, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->driverType:I

    invoke-virtual {v2, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 272
    invoke-direct {p0, v3, p1, p2}, Lcom/example/helloworlddashboard/model/CarServiceModel;->acknowledge(III)V

    .line 273
    invoke-direct {p0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateKeepOnMode(I)V

    const/4 p1, 0x7

    if-eq v0, p1, :cond_2

    const/16 p1, 0x8

    if-ne v0, p1, :cond_1

    goto :goto_0

    :cond_1
    move p1, v4

    goto :goto_1

    :cond_2
    :goto_0
    move p1, v5

    .line 276
    :goto_1
    iget p2, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    if-eq v0, p2, :cond_3

    move v4, v5

    .line 277
    :cond_3
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->enterTheme()V

    .line 278
    iput v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    .line 279
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "themeSwitch update currTheme value. mCurrTheme:"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-static {v1, p2}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    if-nez v4, :cond_4

    if-eqz p1, :cond_5

    .line 282
    :cond_4
    iget-object p2, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->fullNaviChangeTask:Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;

    invoke-virtual {p2, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;->setMeterNaviRunning(Z)V

    .line 283
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object p1

    iget-object p2, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->fullNaviChangeTask:Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;

    const-wide/16 v0, 0x15e

    invoke-virtual {p1, p2, v0, v1}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 285
    :cond_5
    iget p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    invoke-direct {p0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateMeterState(I)V

    return-void

    .line 263
    :cond_6
    :goto_2
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "requestValueToThemeValueAndDriveType fail. requestValue:"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method private updateFusionState(Z)V
    .locals 3

    const-string v0, "SignTestHelper"

    .line 1061
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "updateFusionState canFusion:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 1062
    monitor-enter p0

    .line 1063
    :try_start_0
    iput-boolean p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->canFusion:Z

    .line 1064
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz p1, :cond_0

    const/4 p1, 0x2

    goto :goto_0

    :cond_0
    const/4 p1, 0x1

    .line 1072
    :goto_0
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    const v1, 0x2140f47d

    const/4 v2, 0x0

    invoke-static {v0, v1, v2, p1}, Lcom/example/helloworlddashboard/utils/DataUtil;->setCabinIntValue(Landroid/car/hardware/cabin/CarCabinManager;III)V

    return-void

    :catchall_0
    move-exception p1

    .line 1064
    :try_start_1
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method private updateKeepOnMode()V
    .locals 3

    .line 149
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/ThemeChangeHelp;->isNight(Landroid/content/Context;)Z

    move-result v0

    .line 150
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "isNight  :"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "SignTestHelper"

    invoke-static {v2, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 151
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->updateKeepOnMode(Landroid/content/Context;Z)V

    return-void
.end method

.method private updateKeepOnMode(I)V
    .locals 4

    .line 441
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->getCurrTheme()I

    move-result v0

    .line 442
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "updateKeepOnMode requestValue:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " , currTheme:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "SignTestHelper"

    invoke-static {v2, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v1, 0x0

    const/16 v2, 0x12

    if-eq v2, p1, :cond_5

    const/16 v2, 0x19

    if-ne v2, p1, :cond_0

    goto :goto_1

    :cond_0
    const/16 v2, 0x16

    const/4 v3, 0x1

    if-eq v2, p1, :cond_3

    const/16 v2, 0x10

    if-ne v2, p1, :cond_1

    goto :goto_0

    :cond_1
    const/16 v0, 0x17

    if-eq v0, p1, :cond_6

    const/16 v0, 0x11

    if-eq v0, p1, :cond_6

    const/16 v0, 0x1d

    if-ne v0, p1, :cond_2

    goto :goto_2

    .line 457
    :cond_2
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    invoke-static {p1, v3}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->updateKeepOnMode(Landroid/content/Context;Z)V

    goto :goto_2

    .line 450
    :cond_3
    :goto_0
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    const/4 v2, 0x3

    if-eq v2, v0, :cond_4

    const/16 v2, 0x8

    if-eq v2, v0, :cond_4

    move v1, v3

    :cond_4
    invoke-static {p1, v1}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->updateKeepOnMode(Landroid/content/Context;Z)V

    goto :goto_2

    .line 446
    :cond_5
    :goto_1
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    invoke-static {p1, v1}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->updateKeepOnMode(Landroid/content/Context;Z)V

    :cond_6
    :goto_2
    return-void
.end method

.method private updateMeterState(I)V
    .locals 5

    .line 480
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    if-eqz v0, :cond_1

    .line 482
    monitor-enter p0

    .line 483
    :try_start_0
    iget-boolean v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->canFusion:Z

    .line 484
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 485
    invoke-static {p1}, Lcom/example/helloworlddashboard/utils/DataUtil;->themeValueToSettingStateValue(I)I

    move-result v1

    const/4 v2, 0x4

    if-ne v1, v2, :cond_0

    if-eqz v0, :cond_0

    const/4 v1, 0x5

    .line 495
    :cond_0
    iget-object v2, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    invoke-virtual {v2}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v2

    const-string v3, "key_meter_page_state"

    invoke-static {v2, v3, v1}, Landroid/provider/Settings$Global;->putInt(Landroid/content/ContentResolver;Ljava/lang/String;I)Z

    const-string v2, "SignTestHelper"

    .line 496
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "updateMeterState themeValue:"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v3, " , result:"

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, " , canFusionValue:"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v2, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :catchall_0
    move-exception p1

    .line 484
    :try_start_1
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1

    :cond_1
    :goto_0
    return-void
.end method

.method private voiceFullNaviRequest(Z)V
    .locals 2

    .line 736
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "voiceFullNaviRequest isEnterOrHide:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " , interactiveProtocol:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "SignTestHelper"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 737
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    if-eqz v0, :cond_1

    .line 738
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-interface {v0, p1}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->meterFullNaviRequest(Z)Z

    :cond_1
    return-void
.end method


# virtual methods
.method public AvmChange(I)V
    .locals 7

    const/16 v0, 0x63

    const/4 v1, 0x1

    const/4 v2, 0x2

    if-eq v2, p1, :cond_0

    if-eq v1, p1, :cond_0

    if-eq v0, p1, :cond_0

    return-void

    .line 377
    :cond_0
    iget-object v3, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    if-eqz v3, :cond_5

    .line 379
    invoke-static {}, Lcom/example/helloworlddashboard/utils/ProfileUtil;->isBuiltin()Z

    move-result v3

    const/4 v4, 0x0

    if-eqz v3, :cond_1

    .line 381
    invoke-static {}, Lcom/example/helloworlddashboard/utils/ProfileUtil;->getAvmLimitSpeed()I

    move-result v3

    int-to-float v3, v3

    iget-object v5, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarMcuManager:Landroid/car/hardware/mcu/CarMcuManager;

    const v6, 0x11600207

    invoke-static {v5, v6, v4}, Lcom/example/helloworlddashboard/utils/DataUtil;->getCarMcuFloatValue(Landroid/car/hardware/mcu/CarMcuManager;II)F

    move-result v5

    invoke-static {v3, v5}, Ljava/lang/Float;->compare(FF)I

    move-result v3

    if-lez v3, :cond_4

    if-eq v2, p1, :cond_3

    if-ne v0, p1, :cond_2

    goto :goto_0

    :cond_1
    if-eq v2, p1, :cond_3

    if-ne v0, p1, :cond_2

    goto :goto_0

    :cond_2
    move v1, v4

    :cond_3
    :goto_0
    move v4, v1

    .line 387
    :cond_4
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-interface {p1, v4}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->avmDisplaySwitch(Z)V

    :cond_5
    return-void
.end method

.method public appShutdown()V
    .locals 1

    .line 1095
    invoke-super {p0}, Lcom/example/helloworlddashboard/model/BaseServiceModel;->appShutdown()V

    const/4 v0, 0x1

    .line 1096
    invoke-direct {p0, v0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->exitCastScreen(Z)V

    return-void
.end method

.method public applyThemeRestore()V
    .locals 5

    .line 986
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->isPowerOn()Z

    move-result v0

    const-string v1, "SignTestHelper"

    if-nez v0, :cond_0

    const-string v0, "applyThemeRestore fail. is not power on."

    .line 987
    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 990
    :cond_0
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->getCurrentThemeStatus()I

    move-result v0

    .line 991
    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/DataUtil;->themeValueToDriveType(I)I

    move-result v2

    .line 992
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "applyThemeRestore themeValue:"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, " , tempDriveType:"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v1, v3}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v1, -0x1

    if-ne v1, v2, :cond_1

    return-void

    .line 996
    :cond_1
    iput v2, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->driverType:I

    .line 997
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->enterTheme()V

    .line 998
    iput v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    return-void
.end method

.method public enterMapScreen(I)V
    .locals 5

    .line 294
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "enterMapScreen="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;)V

    const/4 v0, 0x1

    const/4 v1, 0x4

    const/4 v2, 0x0

    if-eq p1, v1, :cond_4

    const/16 v3, 0x12

    if-ne p1, v3, :cond_0

    goto :goto_2

    :cond_0
    const/16 v1, 0x19

    const/16 v3, 0x18

    if-eq p1, v1, :cond_2

    if-ne p1, v3, :cond_1

    goto :goto_0

    .line 311
    :cond_1
    invoke-direct {p0, v2}, Lcom/example/helloworlddashboard/model/CarServiceModel;->screenFullNavi(Z)V

    .line 313
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->exitFullNaviPresentation()V

    goto :goto_5

    .line 306
    :cond_2
    :goto_0
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-interface {v1}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->isNaviRunning()Z

    move-result v1

    if-ne p1, v3, :cond_3

    goto :goto_1

    :cond_3
    move v0, v2

    :goto_1
    invoke-virtual {p0, v1, v0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->showOrHideSimpleNaviPresentation(ZZ)V

    goto :goto_5

    .line 297
    :cond_4
    :goto_2
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->isLightShowing()Z

    move-result v3

    invoke-direct {p0, v2, v3}, Lcom/example/helloworlddashboard/model/CarServiceModel;->stopSimpleNaviPresentation(ZZ)V

    .line 298
    iget-object v3, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    if-ne p1, v1, :cond_5

    goto :goto_3

    :cond_5
    move v0, v2

    :goto_3
    const/4 v4, 0x0

    invoke-static {v3, v2, v0, v4}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showMapMode(Landroid/content/Context;IZLcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    if-ne p1, v1, :cond_6

    const/4 p1, 0x7

    goto :goto_4

    :cond_6
    const/16 p1, 0x8

    .line 301
    :goto_4
    iput p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    .line 303
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->receiveMeterFullNavi()V

    .line 315
    :goto_5
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissPlayAim()V

    return-void
.end method

.method public exitFullNaviPresentation()V
    .locals 1

    const/4 v0, -0x1

    .line 1083
    iput v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    .line 1084
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissMap()V

    return-void
.end method

.method public isAvmOn()Z
    .locals 4

    .line 394
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    const v1, 0x2140f431

    const/4 v2, 0x0

    invoke-static {v0, v1, v2}, Lcom/example/helloworlddashboard/utils/DataUtil;->getCarCabinIntValue(Landroid/car/hardware/cabin/CarCabinManager;II)I

    move-result v0

    .line 396
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "avmSwitchValue="

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v3, "SignTestHelper"

    invoke-static {v3, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v1, 0x2

    if-eq v1, v0, :cond_0

    const/16 v1, 0x63

    if-ne v1, v0, :cond_1

    :cond_0
    const/4 v2, 0x1

    :cond_1
    return v2
.end method

.method public isLightShowing()Z
    .locals 3

    const/16 v0, 0x16

    .line 1054
    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isSpecialAnimShowing(I)Z

    move-result v0

    .line 1055
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "isLightShowing result:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "SignTestHelper"

    invoke-static {v2, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v0
.end method

.method public isMeterNaviRunning()Z
    .locals 3

    .line 1010
    iget v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    const/4 v1, 0x7

    if-eq v0, v1, :cond_1

    const/16 v1, 0x8

    if-ne v0, v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    .line 1011
    :goto_1
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "isMeterNaviRunning mCurrTheme:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget v2, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " , result:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "SignTestHelper"

    invoke-static {v2, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v0
.end method

.method public isRestMode()Z
    .locals 3

    const/16 v0, 0x14

    .line 1046
    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isSpecialAnimShowing(I)Z

    move-result v0

    .line 1047
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "isRestMode result:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "SignTestHelper"

    invoke-static {v2, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v0
.end method

.method public isThemeNormal()Z
    .locals 1

    .line 981
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->haveAbnormal()Z

    move-result v0

    xor-int/lit8 v0, v0, 0x1

    return v0
.end method

.method public isTrackRunning()Z
    .locals 3

    .line 1003
    iget v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    const/4 v1, 0x6

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    .line 1004
    :goto_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "isTrackRunning mCurrTheme:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget v2, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " , result:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "SignTestHelper"

    invoke-static {v2, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v0
.end method

.method public synthetic lambda$new$0$CarServiceModel()V
    .locals 2

    const-string v0, "SignTestHelper"

    const-string v1, "initHeartbeatTask"

    .line 94
    invoke-static {v0, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 95
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->isPowerOn()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 97
    monitor-enter p0

    .line 98
    :try_start_0
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->heartbeatHelp:Lcom/example/helloworlddashboard/utils/HeartbeatHelp;

    if-nez v0, :cond_0

    .line 99
    new-instance v0, Lcom/example/helloworlddashboard/utils/HeartbeatHelp;

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    invoke-direct {v0, v1}, Lcom/example/helloworlddashboard/utils/HeartbeatHelp;-><init>(Landroid/car/hardware/cabin/CarCabinManager;)V

    iput-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->heartbeatHelp:Lcom/example/helloworlddashboard/utils/HeartbeatHelp;

    .line 101
    :cond_0
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->heartbeatHelp:Lcom/example/helloworlddashboard/utils/HeartbeatHelp;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Lcom/example/helloworlddashboard/utils/HeartbeatHelp;->connectRequest(Z)V

    .line 102
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 104
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateKeepOnMode()V

    goto :goto_0

    :catchall_0
    move-exception v0

    .line 102
    :try_start_1
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v0

    .line 106
    :cond_1
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->exitCastScreen()V

    :goto_0
    return-void
.end method

.method public synthetic lambda$new$1$CarServiceModel()V
    .locals 4

    .line 123
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    const v1, 0x2140f3ec

    const/4 v2, 0x0

    const/4 v3, 0x1

    invoke-static {v0, v1, v2, v3}, Lcom/example/helloworlddashboard/utils/DataUtil;->setCabinIntValue(Landroid/car/hardware/cabin/CarCabinManager;III)V

    return-void
.end method

.method public synthetic lambda$new$2$CarServiceModel()V
    .locals 2

    .line 1232
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    const/4 v1, 0x1

    invoke-interface {v0, v1}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->receiveMeterStateChange(Z)V

    return-void
.end method

.method public naviStateChanged(ZZ)V
    .locals 2

    .line 335
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "isNavi="

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const-string v0, "SignTestHelper"

    invoke-static {v0, p2}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const p2, 0x2140f431

    const/4 v1, 0x0

    if-nez p1, :cond_1

    .line 338
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->isMeterNaviRunning()Z

    move-result p1

    if-eqz p1, :cond_0

    .line 340
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->exitFullNaviPresentation()V

    .line 341
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    invoke-static {p1, p2, v1}, Lcom/example/helloworlddashboard/utils/DataUtil;->getCarCabinIntValue(Landroid/car/hardware/cabin/CarCabinManager;II)I

    move-result p1

    const/4 p2, 0x2

    if-ne p2, p1, :cond_0

    .line 344
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-interface {p1}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->isJumpToNavi2ByAvm()Z

    move-result p1

    if-eqz p1, :cond_0

    .line 345
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-interface {p1}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->startNaviDesk()V

    .line 346
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-interface {p1, v1}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->setJumpToNavi2ByAvm(Z)V

    .line 349
    :cond_0
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->isLightShowing()Z

    move-result p1

    invoke-direct {p0, v1, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->stopSimpleNaviPresentation(ZZ)V

    goto :goto_0

    .line 352
    :cond_1
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-interface {p1}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->isNaviTop()Z

    move-result p1

    if-eqz p1, :cond_2

    .line 354
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    invoke-static {p1, p2, v1}, Lcom/example/helloworlddashboard/utils/DataUtil;->getCarCabinIntValue(Landroid/car/hardware/cabin/CarCabinManager;II)I

    move-result p1

    .line 356
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "ID_MCU_AVM_DISPLAY_SWITCH avmSwitchValue:"

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v1, " , interactiveProtocol:"

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-virtual {p2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-static {v0, p2}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 357
    invoke-virtual {p0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->AvmChange(I)V

    goto :goto_0

    :cond_2
    const-string p1, "naviStateChanged runTopAppChangTask "

    .line 361
    invoke-static {v0, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 362
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-interface {p1}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->runTopAppChangTask()V

    :goto_0
    return-void
.end method

.method protected onChangeEvent(Landroid/car/hardware/CarPropertyValue;)V
    .locals 7

    .line 801
    invoke-super {p0, p1}, Lcom/example/helloworlddashboard/model/BaseServiceModel;->onChangeEvent(Landroid/car/hardware/CarPropertyValue;)V

    .line 802
    invoke-virtual {p1}, Landroid/car/hardware/CarPropertyValue;->getPropertyId()I

    move-result v0

    const/16 v1, 0x16

    const/4 v2, 0x1

    const/4 v3, 0x0

    const/4 v4, 0x2

    const-string v5, "SignTestHelper"

    sparse-switch v0, :sswitch_data_0

    goto/16 :goto_0

    .line 863
    :sswitch_0
    invoke-virtual {p1}, Landroid/car/hardware/CarPropertyValue;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Float;

    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    move-result p1

    .line 864
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "onChangeEvent: ID_IP_AFE_AFTER_IGN_ON value:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v5, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    goto/16 :goto_0

    .line 853
    :sswitch_1
    invoke-virtual {p1}, Landroid/car/hardware/CarPropertyValue;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Float;

    invoke-virtual {p1}, Ljava/lang/Float;->floatValue()F

    move-result p1

    .line 854
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "onChangeEvent: ID_IP_TOTALODOMETER"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v5, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    goto/16 :goto_0

    .line 857
    :sswitch_2
    invoke-virtual {p1}, Landroid/car/hardware/CarPropertyValue;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/Integer;

    .line 858
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Received ID_IP_INFORMATION_TRANSMISSION, value = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-static {p1}, Ljava/util/Arrays;->toString([Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v5, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 859
    invoke-direct {p0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->imformationTransmissionRequest([Ljava/lang/Integer;)V

    goto/16 :goto_0

    .line 893
    :sswitch_3
    invoke-virtual {p1}, Landroid/car/hardware/CarPropertyValue;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 894
    iget v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastAvmValue:I

    if-ne v0, p1, :cond_0

    .line 895
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "avmSwitchValue May send values continuously, mask out. lastRequestValue:"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastAvmValue:I

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v5, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 898
    :cond_0
    iput p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastAvmValue:I

    .line 899
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "ID_MCU_AVM_DISPLAY_SWITCH avmSwitchValue:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " , interactiveProtocol:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v5, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 900
    invoke-virtual {p0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->AvmChange(I)V

    goto/16 :goto_0

    .line 910
    :sswitch_4
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "ID_IP_HUTRANSFAULTTYPE value:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v5, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->i(Ljava/lang/String;Ljava/lang/String;)V

    goto/16 :goto_0

    .line 868
    :sswitch_5
    invoke-virtual {p1}, Landroid/car/hardware/CarPropertyValue;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 869
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "onChangeEvent: ID_IP_HUCONNECTREPLY value:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v5, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    if-ne p1, v2, :cond_8

    .line 871
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->heartbeatHelp:Lcom/example/helloworlddashboard/utils/HeartbeatHelp;

    if-eqz p1, :cond_1

    .line 872
    invoke-virtual {p1}, Lcom/example/helloworlddashboard/utils/HeartbeatHelp;->connectReply()V

    goto/16 :goto_0

    :cond_1
    const-string p1, "SIGN_KEEP_CONNECT_REPLY heartbeatHelp is null."

    .line 874
    invoke-static {v5, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto/16 :goto_0

    .line 880
    :sswitch_6
    invoke-virtual {p1}, Landroid/car/hardware/CarPropertyValue;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 881
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "onChangeEvent: ID_IP_HUHEARTBEAT value:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v5, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    if-ne p1, v4, :cond_8

    .line 883
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->heartbeatHelp:Lcom/example/helloworlddashboard/utils/HeartbeatHelp;

    if-eqz p1, :cond_2

    .line 884
    invoke-virtual {p1}, Lcom/example/helloworlddashboard/utils/HeartbeatHelp;->replyHeartbeat()V

    goto/16 :goto_0

    :cond_2
    const-string p1, "SIGN_KEEP_IP_HEARTBEAT heartbeatHelp is null."

    .line 886
    invoke-static {v5, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto/16 :goto_0

    .line 923
    :sswitch_7
    invoke-virtual {p1}, Landroid/car/hardware/CarPropertyValue;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 924
    invoke-direct {p0, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->aiLightStsParse(I)V

    goto/16 :goto_0

    .line 805
    :sswitch_8
    invoke-virtual {p1}, Landroid/car/hardware/CarPropertyValue;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 806
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "onChangeEvent: ID_MCU_ACC_STATE accStatue:"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v5, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    if-ne p1, v4, :cond_8

    .line 811
    iget p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastRequestValue:I

    if-eq v1, p1, :cond_3

    .line 812
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->exitCastScreen()V

    .line 814
    :cond_3
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->isLightShowing()Z

    move-result p1

    invoke-direct {p0, v3, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->stopSimpleNaviPresentation(ZZ)V

    .line 815
    invoke-virtual {p0, v3}, Lcom/example/helloworlddashboard/model/CarServiceModel;->requestMeterFullNavi(Z)V

    .line 816
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->exitFullNaviPresentation()V

    goto/16 :goto_0

    .line 821
    :sswitch_9
    invoke-virtual {p1}, Landroid/car/hardware/CarPropertyValue;->getValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 825
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "onChangeEvent: ID_POWER_STATE value:"

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v6, " , lastRequestValue:"

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget v6, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastRequestValue:I

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v5, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v0, 0x4

    if-ne p1, v0, :cond_5

    .line 828
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->heartbeatHelp:Lcom/example/helloworlddashboard/utils/HeartbeatHelp;

    if-nez p1, :cond_4

    .line 829
    new-instance p1, Lcom/example/helloworlddashboard/utils/HeartbeatHelp;

    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    invoke-direct {p1, v0}, Lcom/example/helloworlddashboard/utils/HeartbeatHelp;-><init>(Landroid/car/hardware/cabin/CarCabinManager;)V

    iput-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->heartbeatHelp:Lcom/example/helloworlddashboard/utils/HeartbeatHelp;

    .line 831
    :cond_4
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->heartbeatHelp:Lcom/example/helloworlddashboard/utils/HeartbeatHelp;

    invoke-virtual {p1, v2}, Lcom/example/helloworlddashboard/utils/HeartbeatHelp;->connectRequest(Z)V

    .line 833
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateKeepOnMode()V

    .line 835
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;

    invoke-interface {p1}, Lcom/example/helloworlddashboard/model/IInteractiveProtocol;->runTopAppChangTask()V

    goto :goto_0

    :cond_5
    if-ne p1, v4, :cond_8

    .line 840
    iget p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastRequestValue:I

    if-eq v1, p1, :cond_6

    const/16 v0, 0x1a

    if-eq v0, p1, :cond_6

    .line 842
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->exitCastScreen()V

    .line 844
    :cond_6
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "power off. lastRequestValue:"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastRequestValue:I

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v5, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 845
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->heartbeatHelp:Lcom/example/helloworlddashboard/utils/HeartbeatHelp;

    if-eqz p1, :cond_7

    .line 846
    invoke-virtual {p1}, Lcom/example/helloworlddashboard/utils/HeartbeatHelp;->pause()V

    .line 848
    :cond_7
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->isLightShowing()Z

    move-result p1

    invoke-direct {p0, v3, p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->stopSimpleNaviPresentation(ZZ)V

    .line 849
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->enterPowerOff()V

    :cond_8
    :goto_0
    return-void

    :sswitch_data_0
    .sparse-switch
        0x2140f05e -> :sswitch_9
        0x2140f0e2 -> :sswitch_8
        0x2140f28e -> :sswitch_7
        0x2140f387 -> :sswitch_6
        0x2140f388 -> :sswitch_5
        0x2140f428 -> :sswitch_4
        0x2140f431 -> :sswitch_3
        0x2141f432 -> :sswitch_2
        0x21600309 -> :sswitch_1
        0x2160f284 -> :sswitch_0
    .end sparse-switch
.end method

.method public onPresentationShow()V
    .locals 0

    return-void
.end method

.method protected onServiceConnected()V
    .locals 0

    .line 783
    invoke-super {p0}, Lcom/example/helloworlddashboard/model/BaseServiceModel;->onServiceConnected()V

    .line 784
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->initHeartbeat()V

    return-void
.end method

.method protected onServiceDisconnected()V
    .locals 1

    .line 789
    invoke-super {p0}, Lcom/example/helloworlddashboard/model/BaseServiceModel;->onServiceDisconnected()V

    .line 790
    invoke-static {}, Lcom/example/helloworlddashboard/utils/Logcat;->e()V

    .line 791
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->heartbeatHelp:Lcom/example/helloworlddashboard/utils/HeartbeatHelp;

    if-eqz v0, :cond_0

    .line 792
    invoke-virtual {v0}, Lcom/example/helloworlddashboard/utils/HeartbeatHelp;->destroy()V

    .line 793
    monitor-enter p0

    const/4 v0, 0x0

    .line 794
    :try_start_0
    iput-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->heartbeatHelp:Lcom/example/helloworlddashboard/utils/HeartbeatHelp;

    .line 795
    monitor-exit p0

    goto :goto_0

    :catchall_0
    move-exception v0

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v0

    :cond_0
    :goto_0
    return-void
.end method

.method public register()V
    .locals 3

    const/4 v0, 0x0

    .line 934
    iput v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->driverType:I

    .line 936
    new-instance v0, Landroid/content/IntentFilter;

    const-string v1, "com.example.helloworlddashboard.Test.Sign"

    invoke-direct {v0, v1}, Landroid/content/IntentFilter;-><init>(Ljava/lang/String;)V

    const-string v1, "android.intent.action.SCREEN_ON"

    .line 937
    invoke-virtual {v0, v1}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    const-string v1, "android.intent.action.SCREEN_OFF"

    .line 938
    invoke-virtual {v0, v1}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    const-string v1, "voice_action_switch_navi"

    .line 939
    invoke-virtual {v0, v1}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    const-string v1, "action_screen_cast"

    .line 940
    invoke-virtual {v0, v1}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    const-string v1, "action_simple_navi"

    .line 941
    invoke-virtual {v0, v1}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    const-string v1, "com.example.helloworlddashboard.ENTERS_THE_INSTRUMENT_NAVIGATION"

    .line 942
    invoke-virtual {v0, v1}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    .line 943
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    iget-object v2, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->broadcastReceiver:Landroid/content/BroadcastReceiver;

    invoke-virtual {v1, v2, v0}, Landroid/content/Context;->registerReceiver(Landroid/content/BroadcastReceiver;Landroid/content/IntentFilter;)Landroid/content/Intent;

    .line 945
    invoke-super {p0}, Lcom/example/helloworlddashboard/model/BaseServiceModel;->register()V

    .line 947
    new-instance v0, Lcom/example/helloworlddashboard/utils/TestMockHelp;

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinEventCallback:Landroid/car/hardware/cabin/CarCabinManager$CarCabinEventCallback;

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/example/helloworlddashboard/utils/TestMockHelp;-><init>(Landroid/car/hardware/cabin/CarCabinManager$CarCabinEventCallback;Landroid/car/hardware/mcu/CarMcuManager$CarMcuEventCallback;)V

    iput-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->testMockHelp:Lcom/example/helloworlddashboard/utils/TestMockHelp;

    return-void
.end method

.method public removeFullNaviChangeTask()V
    .locals 2

    .line 1077
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "removeFullNaviChangeTask fullNaviChangeTask.meterNaviRunning:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->fullNaviChangeTask:Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;

    invoke-static {v1}, Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;->access$100(Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;)Z

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "SignTestHelper"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 1078
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->fullNaviChangeTask:Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    return-void
.end method

.method public requestFitnessAnimChange(IZZZZIIIIIILjava/lang/String;Ljava/lang/String;)Z
    .locals 15

    .line 193
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isFitnessAnimShowing()Z

    move-result v0

    if-eqz v0, :cond_0

    move-object v0, p0

    .line 194
    iget-object v1, v0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    move/from16 v2, p1

    move/from16 v3, p2

    move/from16 v4, p3

    move/from16 v5, p4

    move/from16 v6, p5

    move/from16 v7, p6

    move/from16 v8, p7

    move/from16 v9, p8

    move/from16 v10, p9

    move/from16 v11, p10

    move/from16 v12, p11

    move-object/from16 v13, p12

    move-object/from16 v14, p13

    invoke-static/range {v1 .. v14}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->ExecFitnessCmd(Landroid/content/Context;IZZZZIIIIIILjava/lang/String;Ljava/lang/String;)Z

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    move-object v0, p0

    const/4 v1, 0x0

    :goto_0
    return v1
.end method

.method public requestMeterFullNavi(Z)V
    .locals 4

    .line 1018
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "requestMeterFullNavi isEnterOrExit:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "SignTestHelper"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 1019
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->lastFlyingScreenTime:J

    .line 1025
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->fullNaviChangeTask:Lcom/example/helloworlddashboard/model/CarServiceModel$FullNaviChangeTask;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    .line 1026
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    if-eqz p1, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v1, 0x2

    :goto_0
    const v2, 0x2140f2bb

    const/4 v3, 0x0

    invoke-static {v0, v2, v3, v1}, Lcom/example/helloworlddashboard/utils/DataUtil;->setCabinIntValue(Landroid/car/hardware/cabin/CarCabinManager;III)V

    .line 1027
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->ipScreenReqClearTask:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    .line 1028
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->ipScreenReqClearTask:Ljava/lang/Runnable;

    const-wide/16 v2, 0x12c

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    if-nez p1, :cond_1

    const/4 p1, -0x1

    .line 1030
    iput p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCurrTheme:I

    :cond_1
    return-void
.end method

.method public requestMeterSimpleNavi(Z)V
    .locals 3

    .line 1036
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "requestMeterSimpleNavi isEnterOrExit:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "SignTestHelper"

    invoke-static {v0, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 1040
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    const v0, 0x2140f3ec

    const/4 v1, 0x0

    const/4 v2, 0x2

    invoke-static {p1, v0, v1, v2}, Lcom/example/helloworlddashboard/utils/DataUtil;->setCabinIntValue(Landroid/car/hardware/cabin/CarCabinManager;III)V

    return-void
.end method

.method public declared-synchronized setInteractiveProtocol(Lcom/example/helloworlddashboard/model/IInteractiveProtocol;)V
    .locals 3

    monitor-enter p0

    :try_start_0
    const-string v0, "SignTestHelper"

    .line 1089
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "setInteractiveProtocol protocol:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 1090
    iput-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->interactiveProtocol:Lcom/example/helloworlddashboard/model/IInteractiveProtocol;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 1091
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public showOrHideSimpleNaviPresentation(ZZ)V
    .locals 2

    .line 409
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "isNavi="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, "  isNight="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "SignTestHelper"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    if-eqz p1, :cond_0

    .line 411
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    const/4 v0, 0x0

    invoke-static {p1, p2, v0}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showSimpleMap(Landroid/content/Context;ZLcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    goto :goto_0

    .line 413
    :cond_0
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissSimpleMap()V

    :goto_0
    return-void
.end method

.method public themeChange()V
    .locals 2

    const-string v0, "SignTestHelper"

    const-string v1, "theme changed"

    .line 403
    invoke-static {v0, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 404
    invoke-direct {p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->updateKeepOnMode()V

    return-void
.end method

.method public unregister()V
    .locals 3

    const/4 v0, 0x0

    .line 952
    iput-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCallback:Lcom/example/helloworlddashboard/callback/CarServiceCallback;

    .line 953
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->destroy()V

    .line 954
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    if-eqz v1, :cond_0

    .line 956
    :try_start_0
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    iget-object v2, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinEventCallback:Landroid/car/hardware/cabin/CarCabinManager$CarCabinEventCallback;

    invoke-virtual {v1, v2}, Landroid/car/hardware/cabin/CarCabinManager;->unregisterCallback(Landroid/car/hardware/cabin/CarCabinManager$CarCabinEventCallback;)V

    .line 957
    iput-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v1

    .line 959
    invoke-virtual {v1}, Ljava/lang/Exception;->printStackTrace()V

    .line 962
    :cond_0
    :goto_0
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCar:Landroid/car/Car;

    if-eqz v1, :cond_1

    .line 963
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCar:Landroid/car/Car;

    invoke-virtual {v1}, Landroid/car/Car;->disconnect()V

    .line 964
    iput-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mCar:Landroid/car/Car;

    .line 967
    :cond_1
    :try_start_1
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->mContext:Landroid/content/Context;

    iget-object v2, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->broadcastReceiver:Landroid/content/BroadcastReceiver;

    invoke-virtual {v1, v2}, Landroid/content/Context;->unregisterReceiver(Landroid/content/BroadcastReceiver;)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_1

    goto :goto_1

    :catch_1
    move-exception v1

    .line 969
    invoke-virtual {v1}, Ljava/lang/Exception;->printStackTrace()V

    .line 971
    :goto_1
    iput-object v0, p0, Lcom/example/helloworlddashboard/model/CarServiceModel;->testMockHelp:Lcom/example/helloworlddashboard/utils/TestMockHelp;

    return-void
.end method
