.class public Lcom/example/helloworlddashboard/utils/CarSignConstant;
.super Ljava/lang/Object;
.source "CarSignConstant.java"


# static fields
.field public static final CABIN_PROPERTIES:[I

.field public static final LOW_CONFIG_CABIN_PROPERTIES:[I

.field public static final MCU_PROPERTIES:[I

.field public static final SIGN_DRIVE_TIME_HOUR:I = 0x2140f377

.field public static final SIGN_DRIVE_TIME_MINUTE:I = 0x2140f378

.field public static final SIGN_IP_DTE:I = 0x2140f372

.field public static final SIGN_KEEP_CONNECT_REPLY:I = 0x2140f388

.field public static final SIGN_KEEP_CONNECT_REQUEST:I = 0x2140f38e

.field public static final SIGN_KEEP_HU_HEARTBEAT:I = 0x2140f38d

.field public static final SIGN_KEEP_IP_HEARTBEAT:I = 0x2140f387

.field public static final SIGN_RISKS_AVOID_TIMES:I = 0x2140f373

.field public static final SIGN_SMART_MILEAGE:I = 0x2160f375

.field public static final SIGN_TOTAL_RISKS_AVOID_TIMES:I = 0x2140f374

.field public static final SIGN_TOTAL_SMART_MILEAGE:I = 0x2160f376


# direct methods
.method static constructor <clinit>()V
    .locals 3

    const/16 v0, 0xa

    new-array v0, v0, [I

    .line 63
    fill-array-data v0, :array_0

    sput-object v0, Lcom/example/helloworlddashboard/utils/CarSignConstant;->CABIN_PROPERTIES:[I

    const/4 v0, 0x1

    new-array v0, v0, [I

    const/4 v1, 0x0

    const v2, 0x2140f0e2

    aput v2, v0, v1

    .line 75
    sput-object v0, Lcom/example/helloworlddashboard/utils/CarSignConstant;->MCU_PROPERTIES:[I

    const/4 v0, 0x2

    new-array v0, v0, [I

    .line 82
    fill-array-data v0, :array_1

    sput-object v0, Lcom/example/helloworlddashboard/utils/CarSignConstant;->LOW_CONFIG_CABIN_PROPERTIES:[I

    return-void

    nop

    :array_0
    .array-data 4
        0x2140f05e
        0x2141f432
        0x21600309
        0x2160f284
        0x2140f388
        0x2140f387
        0x2140f431
        0x2140f3e6
        0x2140f428
        0x2140f28e
    .end array-data

    :array_1
    .array-data 4
        0x2140f05e
        0x21600309
    .end array-data
.end method

.method public constructor <init>()V
    .locals 0

    .line 12
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
