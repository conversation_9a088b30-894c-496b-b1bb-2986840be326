<?xml version="1.0" encoding="utf-8"?>
<merge android:id="@id/material_timepicker_container" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.Guideline android:orientation="horizontal" android:id="@id/guideline" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintGuide_begin="16.0dip" />
    <include android:id="@id/material_clock_display" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" app:layout_constraintBottom_toTopOf="@id/material_clock_period_toggle" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="@id/guideline" app:layout_constraintVertical_chainStyle="packed" layout="@layout/material_clock_display" />
    <include android:id="@id/material_clock_period_toggle" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="@id/material_clock_display" app:layout_constraintRight_toRightOf="@id/material_clock_display" app:layout_constraintTop_toBottomOf="@id/material_clock_display" app:layout_constraintVertical_chainStyle="packed" layout="@layout/material_clock_period_toggle_land" />
    <com.google.android.material.timepicker.ClockFaceView android:id="@id/material_clock_face" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginStart="@dimen/clock_face_margin_start" app:layout_constraintStart_toEndOf="@id/material_clock_display" app:layout_constraintTop_toTopOf="parent" />
</merge>