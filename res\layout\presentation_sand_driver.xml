<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:id="@id/fl_content" android:focusable="false" android:focusableInTouchMode="false" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextureView android:id="@id/sv_main_surface" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <ImageView android:id="@id/img" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <TextView android:textSize="15.0px" android:textColor="@android:color/holo_red_dark" android:id="@id/tv_mark" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="m" />
</FrameLayout>