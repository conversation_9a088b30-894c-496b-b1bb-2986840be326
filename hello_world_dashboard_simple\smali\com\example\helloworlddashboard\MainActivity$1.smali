.class Lcom/example/helloworlddashboard/MainActivity$1;
.super Ljava/lang/Object;
.source "MainActivity.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/example/helloworlddashboard/MainActivity;->onCreate(Landroid/os/Bundle;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/example/helloworlddashboard/MainActivity;


# direct methods
.method constructor <init>(Lcom/example/helloworlddashboard/MainActivity;)V
    .locals 0

    .line 19
    iput-object p1, p0, Lcom/example/helloworlddashboard/MainActivity$1;->this$0:Lcom/example/helloworlddashboard/MainActivity;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 1

    .line 22
    iget-object p1, p0, Lcom/example/helloworlddashboard/MainActivity$1;->this$0:Lcom/example/helloworlddashboard/MainActivity;

    invoke-static {p1}, Lcom/example/helloworlddashboard/MainActivity;->access$000(Lcom/example/helloworlddashboard/MainActivity;)V

    return-void
.end method
.end class
