.class public Lcom/example/helloworlddashboard/MainActivity;
.super Landroid/app/Activity;
.source "MainActivity.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 10
    invoke-direct {p0}, Landroid/app/Activity;-><init>()V

    return-void
.end method

.method private testMCUFullNaviOn()V
    .locals 4

    const-string v0, "DashboardTest"

    const-string v1, "MCU Full Navigation ON - requestMeterFullNavi(true)"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Sending MCU command: propertyId=0x2140f2bb, value=1"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Settings.Global.putInt(key_meter_page_state, 4)"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void
.end method

.method private testMCUFullNaviOff()V
    .locals 4

    const-string v0, "DashboardTest"

    const-string v1, "MCU Full Navigation OFF - requestMeterFullNavi(false)"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Sending MCU command: propertyId=0x2140f2bb, value=2"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Settings.Global.putInt(key_meter_page_state, -1)"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void
.end method

.method private testMCUSimpleNaviOn()V
    .locals 4

    const-string v0, "DashboardTest"

    const-string v1, "MCU Simple Navigation ON - requestMeterSimpleNavi(true)"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Sending MCU command for simple navigation ON"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void
.end method

.method private testMCUSimpleNaviOff()V
    .locals 4

    const-string v0, "DashboardTest"

    const-string v1, "MCU Simple Navigation OFF - requestMeterSimpleNavi(false)"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Sending MCU command for simple navigation OFF"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void
.end method


# virtual methods
.method protected onCreate(Landroid/os/Bundle;)V
    .locals 2

    .line 15
    invoke-super {p0, p1}, Landroid/app/Activity;->onCreate(Landroid/os/Bundle;)V

    .line 16
    const v0, 0x7f030000    # @layout/activity_main

    invoke-virtual {p0, v0}, Lcom/example/helloworlddashboard/MainActivity;->setContentView(I)V

    .line 18
    const v0, 0x7f020000    # @+id/btn_full_navi_on

    invoke-virtual {p0, v0}, Lcom/example/helloworlddashboard/MainActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/Button;

    .line 19
    new-instance v1, Lcom/example/helloworlddashboard/MainActivity$1;

    invoke-direct {v1, p0}, Lcom/example/helloworlddashboard/MainActivity$1;-><init>(Lcom/example/helloworlddashboard/MainActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 25
    const v0, 0x7f020001    # @+id/btn_full_navi_off

    invoke-virtual {p0, v0}, Lcom/example/helloworlddashboard/MainActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/Button;

    .line 26
    new-instance v1, Lcom/example/helloworlddashboard/MainActivity$2;

    invoke-direct {v1, p0}, Lcom/example/helloworlddashboard/MainActivity$2;-><init>(Lcom/example/helloworlddashboard/MainActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 32
    const v0, 0x7f020002    # @+id/btn_simple_navi_on

    invoke-virtual {p0, v0}, Lcom/example/helloworlddashboard/MainActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/Button;

    .line 33
    new-instance v1, Lcom/example/helloworlddashboard/MainActivity$3;

    invoke-direct {v1, p0}, Lcom/example/helloworlddashboard/MainActivity$3;-><init>(Lcom/example/helloworlddashboard/MainActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 39
    const v0, 0x7f020003    # @+id/btn_simple_navi_off

    invoke-virtual {p0, v0}, Lcom/example/helloworlddashboard/MainActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/Button;

    .line 40
    new-instance v1, Lcom/example/helloworlddashboard/MainActivity$4;

    invoke-direct {v1, p0}, Lcom/example/helloworlddashboard/MainActivity$4;-><init>(Lcom/example/helloworlddashboard/MainActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    const-string v0, "DashboardTest"

    const-string v1, "Dashboard Test App Started - Ready to test MCU commands"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void
.end method

.method static synthetic access$000(Lcom/example/helloworlddashboard/MainActivity;)V
    .locals 0

    .line 8
    invoke-direct {p0}, Lcom/example/helloworlddashboard/MainActivity;->testMCUFullNaviOn()V

    return-void
.end method

.method static synthetic access$100(Lcom/example/helloworlddashboard/MainActivity;)V
    .locals 0

    .line 8
    invoke-direct {p0}, Lcom/example/helloworlddashboard/MainActivity;->testMCUFullNaviOff()V

    return-void
.end method

.method static synthetic access$200(Lcom/example/helloworlddashboard/MainActivity;)V
    .locals 0

    .line 8
    invoke-direct {p0}, Lcom/example/helloworlddashboard/MainActivity;->testMCUSimpleNaviOn()V

    return-void
.end method

.method static synthetic access$300(Lcom/example/helloworlddashboard/MainActivity;)V
    .locals 0

    .line 8
    invoke-direct {p0}, Lcom/example/helloworlddashboard/MainActivity;->testMCUSimpleNaviOff()V

    return-void
.end method
.end class
