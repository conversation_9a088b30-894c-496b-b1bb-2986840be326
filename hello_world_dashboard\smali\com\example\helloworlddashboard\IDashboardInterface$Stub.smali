.class public abstract Lcom/example/helloworlddashboard/IDashboardInterface$Stub;
.super Landroid/os/Binder;
.source "IDashboardInterface.java"

# interfaces
.implements Lcom/example/helloworlddashboard/IDashboardInterface;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/example/helloworlddashboard/IDashboardInterface;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "Stub"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/example/helloworlddashboard/IDashboardInterface$Stub$Proxy;
    }
.end annotation


# static fields
.field private static final DESCRIPTOR:Ljava/lang/String; = "com.example.helloworlddashboard.IDashboardInterface"

.field static final TRANSACTION_onReceiveMediaSource:I = 0x1

.field static final TRANSACTION_onReceivedMediaInfo:I = 0x2

.field static final TRANSACTION_requestFitnessAnim:I = 0x3


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 63
    invoke-direct {p0}, Landroid/os/Binder;-><init>()V

    const-string v0, "com.example.helloworlddashboard.IDashboardInterface"

    .line 64
    invoke-virtual {p0, p0, v0}, Lcom/example/helloworlddashboard/IDashboardInterface$Stub;->attachInterface(Landroid/os/IInterface;Ljava/lang/String;)V

    return-void
.end method

.method public static asInterface(Landroid/os/IBinder;)Lcom/example/helloworlddashboard/IDashboardInterface;
    .locals 2

    if-nez p0, :cond_0

    const/4 p0, 0x0

    return-object p0

    :cond_0
    const-string v0, "com.example.helloworlddashboard.IDashboardInterface"

    .line 75
    invoke-interface {p0, v0}, Landroid/os/IBinder;->queryLocalInterface(Ljava/lang/String;)Landroid/os/IInterface;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 76
    instance-of v1, v0, Lcom/example/helloworlddashboard/IDashboardInterface;

    if-eqz v1, :cond_1

    .line 77
    check-cast v0, Lcom/example/helloworlddashboard/IDashboardInterface;

    return-object v0

    .line 79
    :cond_1
    new-instance v0, Lcom/example/helloworlddashboard/IDashboardInterface$Stub$Proxy;

    invoke-direct {v0, p0}, Lcom/example/helloworlddashboard/IDashboardInterface$Stub$Proxy;-><init>(Landroid/os/IBinder;)V

    return-object v0
.end method

.method public static getDefaultImpl()Lcom/example/helloworlddashboard/IDashboardInterface;
    .locals 1

    .line 306
    sget-object v0, Lcom/example/helloworlddashboard/IDashboardInterface$Stub$Proxy;->sDefaultImpl:Lcom/example/helloworlddashboard/IDashboardInterface;

    return-object v0
.end method

.method public static setDefaultImpl(Lcom/example/helloworlddashboard/IDashboardInterface;)Z
    .locals 1

    .line 296
    sget-object v0, Lcom/example/helloworlddashboard/IDashboardInterface$Stub$Proxy;->sDefaultImpl:Lcom/example/helloworlddashboard/IDashboardInterface;

    if-nez v0, :cond_1

    if-eqz p0, :cond_0

    .line 300
    sput-object p0, Lcom/example/helloworlddashboard/IDashboardInterface$Stub$Proxy;->sDefaultImpl:Lcom/example/helloworlddashboard/IDashboardInterface;

    const/4 p0, 0x1

    return p0

    :cond_0
    const/4 p0, 0x0

    return p0

    .line 297
    :cond_1
    new-instance p0, Ljava/lang/IllegalStateException;

    const-string v0, "setDefaultImpl() called twice"

    invoke-direct {p0, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p0
.end method


# virtual methods
.method public asBinder()Landroid/os/IBinder;
    .locals 0

    return-object p0
.end method

.method public onTransact(ILandroid/os/Parcel;Landroid/os/Parcel;I)Z
    .locals 18
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroid/os/RemoteException;
        }
    .end annotation

    move/from16 v0, p1

    move-object/from16 v1, p2

    move-object/from16 v2, p3

    const/4 v3, 0x1

    const-string v4, "com.example.helloworlddashboard.IDashboardInterface"

    if-eq v0, v3, :cond_7

    const/4 v5, 0x2

    if-eq v0, v5, :cond_6

    const/4 v5, 0x3

    if-eq v0, v5, :cond_1

    const v5, 0x5f4e5446

    if-eq v0, v5, :cond_0

    .line 161
    invoke-super/range {p0 .. p4}, Landroid/os/Binder;->onTransact(ILandroid/os/Parcel;Landroid/os/Parcel;I)Z

    move-result v0

    return v0

    .line 92
    :cond_0
    invoke-virtual {v2, v4}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    return v3

    .line 127
    :cond_1
    invoke-virtual {v1, v4}, Landroid/os/Parcel;->enforceInterface(Ljava/lang/String;)V

    .line 129
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v5

    .line 131
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v0

    const/4 v4, 0x0

    if-eqz v0, :cond_2

    move v6, v3

    goto :goto_0

    :cond_2
    move v6, v4

    .line 133
    :goto_0
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v0

    if-eqz v0, :cond_3

    move v7, v3

    goto :goto_1

    :cond_3
    move v7, v4

    .line 135
    :goto_1
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v0

    if-eqz v0, :cond_4

    move v8, v3

    goto :goto_2

    :cond_4
    move v8, v4

    .line 137
    :goto_2
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v0

    if-eqz v0, :cond_5

    move v9, v3

    goto :goto_3

    :cond_5
    move v9, v4

    .line 139
    :goto_3
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v10

    .line 141
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v11

    .line 143
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v12

    .line 145
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v13

    .line 147
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v14

    .line 149
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v15

    .line 151
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v16

    .line 153
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v17

    move-object/from16 v4, p0

    .line 154
    invoke-virtual/range {v4 .. v17}, Lcom/example/helloworlddashboard/IDashboardInterface$Stub;->requestFitnessAnim(IZZZZIIIIIILjava/lang/String;Ljava/lang/String;)Z

    move-result v0

    .line 155
    invoke-virtual/range {p3 .. p3}, Landroid/os/Parcel;->writeNoException()V

    .line 156
    invoke-virtual {v2, v0}, Landroid/os/Parcel;->writeInt(I)V

    return v3

    .line 106
    :cond_6
    invoke-virtual {v1, v4}, Landroid/os/Parcel;->enforceInterface(Ljava/lang/String;)V

    .line 108
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v5

    .line 110
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v6

    .line 112
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v7

    .line 114
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v8

    .line 116
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v9

    .line 118
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v10

    .line 120
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readInt()I

    move-result v11

    move-object/from16 v4, p0

    .line 121
    invoke-virtual/range {v4 .. v11}, Lcom/example/helloworlddashboard/IDashboardInterface$Stub;->onReceivedMediaInfo(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;II)V

    .line 122
    invoke-virtual/range {p3 .. p3}, Landroid/os/Parcel;->writeNoException()V

    return v3

    .line 97
    :cond_7
    invoke-virtual {v1, v4}, Landroid/os/Parcel;->enforceInterface(Ljava/lang/String;)V

    .line 99
    invoke-virtual/range {p2 .. p2}, Landroid/os/Parcel;->readString()Ljava/lang/String;

    move-result-object v0

    move-object/from16 v1, p0

    .line 100
    invoke-virtual {v1, v0}, Lcom/example/helloworlddashboard/IDashboardInterface$Stub;->onReceiveMediaSource(Ljava/lang/String;)V

    .line 101
    invoke-virtual/range {p3 .. p3}, Landroid/os/Parcel;->writeNoException()V

    return v3
.end method
