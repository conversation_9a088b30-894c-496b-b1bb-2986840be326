.class public Lcom/dashboard/test/MainActivity;
.super Landroid/app/Activity;
.source "MainActivity.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Landroid/app/Activity;-><init>()V

    return-void
.end method


# virtual methods
.method protected onCreate(Landroid/os/Bundle;)V
    .locals 2

    invoke-super {p0, p1}, Landroid/app/Activity;->onCreate(Landroid/os/Bundle;)V

    const v0, 0x7f030000    # @layout/activity_main

    invoke-virtual {p0, v0}, Lcom/dashboard/test/MainActivity;->setContentView(I)V

    const-string v0, "DashboardTest"

    const-string v1, "Dashboard Test App Started"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void
.end method

.method public onFullNaviOn(Landroid/view/View;)V
    .locals 4

    const-string v0, "DashboardTest"

    const-string v1, "MCU Full Navigation ON - requestMeterFullNavi(true)"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Sending MCU command: propertyId=0x2140f2bb, value=1"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Settings.Global.putInt(key_meter_page_state, 4)"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Full Navigation Mode ENABLED"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    return-void
.end method

.method public onFullNaviOff(Landroid/view/View;)V
    .locals 4

    const-string v0, "DashboardTest"

    const-string v1, "MCU Full Navigation OFF - requestMeterFullNavi(false)"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Sending MCU command: propertyId=0x2140f2bb, value=2"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Settings.Global.putInt(key_meter_page_state, -1)"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Full Navigation Mode DISABLED"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    return-void
.end method

.method public onSimpleNaviOn(Landroid/view/View;)V
    .locals 4

    const-string v0, "DashboardTest"

    const-string v1, "MCU Simple Navigation ON - requestMeterSimpleNavi(true)"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Sending MCU command for simple navigation ON"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Simple Navigation Mode ENABLED"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    return-void
.end method

.method public onSimpleNaviOff(Landroid/view/View;)V
    .locals 4

    const-string v0, "DashboardTest"

    const-string v1, "MCU Simple Navigation OFF - requestMeterSimpleNavi(false)"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Sending MCU command for simple navigation OFF"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "Simple Navigation Mode DISABLED"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    return-void
.end method
.end class
