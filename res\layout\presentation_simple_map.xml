<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:focusable="false" android:focusableInTouchMode="false" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:id="@id/rl_simple_navi" android:layout_width="100.0px" android:layout_height="100.0px" android:layout_marginTop="204.0px" android:layout_marginStart="1515.0px">
        <ImageView android:id="@id/img_simple_navi_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        <ImageView android:id="@id/img_simple_navi" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="fitXY" />
    </RelativeLayout>
    <RelativeLayout android:gravity="center" android:id="@id/rl_track" android:visibility="gone" android:layout_width="188.0px" android:layout_height="188.0px" android:layout_marginTop="385.0px" android:layout_marginStart="867.0px">
        <ImageView android:id="@id/img_simple_track_bg" android:background="@drawable/simp_navi_track_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        <ImageView android:id="@id/img_simple_track" android:layout_width="120.0px" android:layout_height="120.0px" android:layout_centerInParent="true" />
    </RelativeLayout>
</FrameLayout>