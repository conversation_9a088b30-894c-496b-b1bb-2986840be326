.class public Lcom/example/helloworlddashboard/DashboardService;
.super Landroid/app/Service;
.source "DashboardService.java"

# interfaces
.implements Lcom/example/helloworlddashboard/callback/CarServiceCallback;


# static fields
.field private static final ACTION_BOOT_HU:Ljava/lang/String; = "android.intent.action.ACTION_BOOT_HU"

.field private static final ACTION_SHUTDOWN_HU:Ljava/lang/String; = "android.intent.action.ACTION_SHUTDOWN_HU"

.field private static final TAG:Ljava/lang/String; = "DashboardService"


# instance fields
.field private final dashboardBinder:Lcom/example/helloworlddashboard/IDashboardInterface$Stub;

.field private final lock:Ljava/lang/Object;

.field private mDashboardModel:Lcom/example/helloworlddashboard/model/DashboardModel;

.field private mIsHighLevelDashboard:Z

.field private mKillSelf:Z

.field private final mReceiver:Landroid/content/BroadcastReceiver;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 36
    invoke-direct {p0}, Landroid/app/Service;-><init>()V

    .line 40
    new-instance v0, Ljava/lang/Object;

    invoke-direct {v0}, Ljava/lang/Object;-><init>()V

    iput-object v0, p0, Lcom/example/helloworlddashboard/DashboardService;->lock:Ljava/lang/Object;

    .line 54
    new-instance v0, Lcom/example/helloworlddashboard/DashboardService$1;

    invoke-direct {v0, p0}, Lcom/example/helloworlddashboard/DashboardService$1;-><init>(Lcom/example/helloworlddashboard/DashboardService;)V

    iput-object v0, p0, Lcom/example/helloworlddashboard/DashboardService;->mReceiver:Landroid/content/BroadcastReceiver;

    .line 85
    new-instance v0, Lcom/example/helloworlddashboard/DashboardService$2;

    invoke-direct {v0, p0}, Lcom/example/helloworlddashboard/DashboardService$2;-><init>(Lcom/example/helloworlddashboard/DashboardService;)V

    iput-object v0, p0, Lcom/example/helloworlddashboard/DashboardService;->dashboardBinder:Lcom/example/helloworlddashboard/IDashboardInterface$Stub;

    return-void
.end method

.method static synthetic access$000(Lcom/example/helloworlddashboard/DashboardService;)Lcom/example/helloworlddashboard/model/DashboardModel;
    .locals 0

    .line 36
    iget-object p0, p0, Lcom/example/helloworlddashboard/DashboardService;->mDashboardModel:Lcom/example/helloworlddashboard/model/DashboardModel;

    return-object p0
.end method

.method static synthetic access$100(Lcom/example/helloworlddashboard/DashboardService;)Ljava/lang/Object;
    .locals 0

    .line 36
    iget-object p0, p0, Lcom/example/helloworlddashboard/DashboardService;->lock:Ljava/lang/Object;

    return-object p0
.end method

.method static synthetic access$200(Lcom/example/helloworlddashboard/DashboardService;)Z
    .locals 0

    .line 36
    iget-boolean p0, p0, Lcom/example/helloworlddashboard/DashboardService;->mIsHighLevelDashboard:Z

    return p0
.end method

.method private unregister()V
    .locals 1

    .line 197
    iget-object v0, p0, Lcom/example/helloworlddashboard/DashboardService;->mDashboardModel:Lcom/example/helloworlddashboard/model/DashboardModel;

    if-eqz v0, :cond_0

    .line 198
    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/DashboardModel;->unregister()V

    const/4 v0, 0x0

    .line 199
    iput-object v0, p0, Lcom/example/helloworlddashboard/DashboardService;->mDashboardModel:Lcom/example/helloworlddashboard/model/DashboardModel;

    .line 202
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/helloworlddashboard/DashboardService;->mReceiver:Landroid/content/BroadcastReceiver;

    invoke-virtual {p0, v0}, Lcom/example/helloworlddashboard/DashboardService;->unregisterReceiver(Landroid/content/BroadcastReceiver;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 204
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    :goto_0
    return-void
.end method


# virtual methods
.method public onBind(Landroid/content/Intent;)Landroid/os/IBinder;
    .locals 0

    .line 150
    iget-object p1, p0, Lcom/example/helloworlddashboard/DashboardService;->dashboardBinder:Lcom/example/helloworlddashboard/IDashboardInterface$Stub;

    return-object p1
.end method

.method public onCarServiceConnected()V
    .locals 0

    .line 210
    invoke-static {}, Lcom/example/helloworlddashboard/utils/Logcat;->d()V

    return-void
.end method

.method public onCreate()V
    .locals 2

    .line 155
    invoke-super {p0}, Landroid/app/Service;->onCreate()V

    const/4 v0, 0x1

    .line 158
    iput-boolean v0, p0, Lcom/example/helloworlddashboard/DashboardService;->mIsHighLevelDashboard:Z

    .line 159
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "mIsHighLevelDashboard = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-boolean v1, p0, Lcom/example/helloworlddashboard/DashboardService;->mIsHighLevelDashboard:Z

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 160
    iget-object v0, p0, Lcom/example/helloworlddashboard/DashboardService;->mDashboardModel:Lcom/example/helloworlddashboard/model/DashboardModel;

    if-nez v0, :cond_1

    .line 161
    iget-boolean v0, p0, Lcom/example/helloworlddashboard/DashboardService;->mIsHighLevelDashboard:Z

    if-eqz v0, :cond_0

    .line 163
    new-instance v0, Lcom/example/helloworlddashboard/model/HighModel;

    invoke-virtual {p0}, Lcom/example/helloworlddashboard/DashboardService;->getApplicationContext()Landroid/content/Context;

    move-result-object v1

    invoke-direct {v0, v1}, Lcom/example/helloworlddashboard/model/HighModel;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/example/helloworlddashboard/DashboardService;->mDashboardModel:Lcom/example/helloworlddashboard/model/DashboardModel;

    goto :goto_0

    .line 166
    :cond_0
    new-instance v0, Lcom/example/helloworlddashboard/model/LowModel;

    invoke-direct {v0}, Lcom/example/helloworlddashboard/model/LowModel;-><init>()V

    iput-object v0, p0, Lcom/example/helloworlddashboard/DashboardService;->mDashboardModel:Lcom/example/helloworlddashboard/model/DashboardModel;

    .line 168
    :goto_0
    iget-object v0, p0, Lcom/example/helloworlddashboard/DashboardService;->mDashboardModel:Lcom/example/helloworlddashboard/model/DashboardModel;

    invoke-virtual {p0}, Lcom/example/helloworlddashboard/DashboardService;->getApplicationContext()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/example/helloworlddashboard/model/DashboardModel;->register(Landroid/content/Context;)V

    .line 170
    :cond_1
    new-instance v0, Landroid/content/IntentFilter;

    const-string v1, "android.intent.action.ACTION_SHUTDOWN_HU"

    invoke-direct {v0, v1}, Landroid/content/IntentFilter;-><init>(Ljava/lang/String;)V

    const-string v1, "android.intent.action.ACTION_BOOT_HU"

    .line 171
    invoke-virtual {v0, v1}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    .line 172
    iget-object v1, p0, Lcom/example/helloworlddashboard/DashboardService;->mReceiver:Landroid/content/BroadcastReceiver;

    invoke-virtual {p0, v1, v0}, Lcom/example/helloworlddashboard/DashboardService;->registerReceiver(Landroid/content/BroadcastReceiver;Landroid/content/IntentFilter;)Landroid/content/Intent;

    return-void
.end method

.method public onDestroy()V
    .locals 1

    .line 185
    invoke-super {p0}, Landroid/app/Service;->onDestroy()V

    .line 186
    invoke-direct {p0}, Lcom/example/helloworlddashboard/DashboardService;->unregister()V

    .line 187
    iget-boolean v0, p0, Lcom/example/helloworlddashboard/DashboardService;->mKillSelf:Z

    if-eqz v0, :cond_0

    const-string v0, "kill self"

    .line 188
    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 189
    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v0

    invoke-static {v0}, Landroid/os/Process;->killProcess(I)V

    goto :goto_0

    .line 191
    :cond_0
    invoke-static {}, Lcom/example/helloworlddashboard/utils/Logcat;->d()V

    :goto_0
    return-void
.end method

.method public onStartCommand(Landroid/content/Intent;II)I
    .locals 0

    .line 179
    invoke-static {}, Lcom/example/helloworlddashboard/utils/Logcat;->d()V

    const/4 p1, 0x1

    return p1
.end method
