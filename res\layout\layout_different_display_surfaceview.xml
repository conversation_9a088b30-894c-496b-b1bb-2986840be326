<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:orientation="vertical" android:background="@color/map_bg_day" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <SurfaceView android:id="@id/displaySurfaceView" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <com.example.mxextend.widget.group.VisibleGroup android:id="@id/group_cross" app:reference_ids="route_guide_raster_image_wrapper,route_guide_lane_time,route_guide_lane_distance" style="@style/WrapperControl" />
    <LinearLayout android:orientation="vertical" android:id="@id/route_guide_info" android:background="@drawable/diff_bg_route_guide_simple_panel" android:visibility="gone" android:layout_width="@dimen/tp_576" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/tp_28" android:layout_marginTop="@dimen/tp_80">
        <RelativeLayout android:id="@id/route_guide_wrapper" android:background="@drawable/bg_route_guide_simple_panel_top" android:layout_width="fill_parent" android:layout_height="@dimen/tp_136">
            <ImageView android:id="@id/route_guide_road_info_turnIcon" android:layout_width="@dimen/tp_100" android:layout_height="@dimen/tp_100" android:layout_marginLeft="@dimen/tp_30" android:layout_marginTop="@dimen/tp_20" android:scaleType="fitXY" />
            <TextView android:textSize="@dimen/tp_60" android:textStyle="bold" android:textColor="@color/full_screen_text_color" android:gravity="center" android:id="@id/route_guide_road_info_distance" android:layout_width="wrap_content" android:layout_height="@dimen/tp_84" android:layout_marginTop="@dimen/tp_8" android:includeFontPadding="false" android:layout_marginStart="@dimen/tp_32" android:layout_toEndOf="@id/route_guide_road_info_turnIcon" />
            <com.example.mxextend.widget.textview.NoSpaceTextView android:textSize="@dimen/tp_28" android:textColor="@color/full_screen_text_50_color" android:id="@id/route_guide_road_info_detail" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignBaseline="@id/route_guide_road_info_distance" android:layout_marginStart="@dimen/tp_12" android:layout_toEndOf="@id/route_guide_road_info_distance" />
            <TextView android:textSize="@dimen/tp_16" android:textColor="@color/white" android:gravity="center" android:id="@id/route_guide_in_out_txt" android:background="@drawable/bg_surface_view_green" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="@dimen/tp_28" android:layout_alignBottom="@id/route_guide_road_info_detail" android:layout_marginStart="@dimen/tp_12" android:layout_toEndOf="@id/route_guide_road_info_detail" android:paddingHorizontal="@dimen/tp_7" />
            <com.example.mxextend.widget.textview.NoSpaceTextView android:textSize="@dimen/tp_28" android:textColor="@color/full_screen_text_50_color" android:id="@id/route_guide_road_info_road_name" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignBottom="@id/route_guide_road_info_turnIcon" android:layout_marginStart="@dimen/tp_32" android:layout_toEndOf="@id/route_guide_road_info_turnIcon" />
        </RelativeLayout>
        <RelativeLayout android:id="@id/route_guide_next_crossing_wrapper" android:background="@color/diff_route_guide_simple_guide_subpanel_bg_color" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="@dimen/tp_88">
            <ImageView android:id="@id/route_guide_next_crossing_icon" android:layout_width="@dimen/tp_64" android:layout_height="@dimen/tp_64" android:scaleType="centerInside" android:layout_marginStart="@dimen/tp_50" android:layout_marginVertical="@dimen/tp_10" />
            <TextView android:textSize="@dimen/tp_24" android:textColor="@color/full_screen_text_70_color" android:id="@id/route_guide_next_crossing_distance" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="44.0px" android:singleLine="true" android:layout_toRightOf="@id/route_guide_next_crossing_icon" android:layout_centerVertical="true" />
        </RelativeLayout>
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <com.example.mxextend.widget.RouteGuideETAView android:id="@id/eta_view" android:layout_width="wrap_content" android:layout_height="wrap_content" />
            <View android:layout_gravity="center" android:id="@id/divider_lane_line" android:background="@color/route_guide_line_color" android:visibility="gone" android:layout_width="@dimen/tp_508" android:layout_height="@dimen/tp_1" />
            <com.example.mxextend.widget.DiffDisPlayLaneInfoView android:id="@id/route_guide_lane_info" android:visibility="gone" android:layout_marginStart="@dimen/tp_28" style="@style/RouteGuideLaneLayoutView" />
        </LinearLayout>
    </LinearLayout>
    <com.example.mxextend.widget.RouteGuidePanelView android:orientation="vertical" android:id="@id/route_guide_panel_view" android:layout_width="@dimen/tp_576" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/tp_28" android:layout_marginTop="@dimen/tp_24" android:layout_below="@id/route_guide_info">
        <include layout="@layout/include_route_guide_panels" />
    </com.example.mxextend.widget.RouteGuidePanelView>
    <RelativeLayout android:id="@id/route_guide_raster_image_wrapper" android:visibility="gone" android:layout_width="@dimen/tp_576" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/tp_28" android:layout_marginTop="@dimen/tp_80">
        <RelativeLayout android:id="@id/route_guide_raster_image_header_bg" android:background="@drawable/diff_bg_route_guide_progerss_top" android:layout_width="fill_parent" android:layout_height="@dimen/tp_80" />
        <ProgressBar android:id="@id/route_guide_raster_image_progressbar_info" android:layout_width="fill_parent" android:layout_height="@dimen/tp_80" android:progressDrawable="@drawable/diff_display_bg_route_guide_progress_bar_info" style="@style/Widget.AppCompat.ProgressBar.Horizontal" />
        <RelativeLayout android:id="@id/route_guide_raster_image_header" android:background="@color/transparent" android:layout_width="fill_parent" android:layout_height="@dimen/tp_80">
            <ImageView android:id="@id/route_guide_raster_image_turn_icon" android:layout_centerVertical="true" style="@style/DisplayGuideCrossTurnIcon" />
            <com.example.mxextend.widget.textview.NoSpaceTextView android:textColor="@color/full_screen_text_color" android:id="@id/route_guide_raster_image_turn_dis" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_toRightOf="@id/route_guide_raster_image_turn_icon" android:layout_centerVertical="true" style="@style/DisplayGuideCrossTurnTv" />
            <com.example.mxextend.widget.textview.NoSpaceTextView android:id="@id/route_guide_raster_image_turn_dis_unit" android:layout_toRightOf="@id/route_guide_raster_image_turn_dis" android:layout_centerVertical="true" style="@style/DisplayNaviSimpleGuideMiniMeter" />
            <com.example.mxextend.widget.textview.NoSpaceTextView android:textColor="@color/full_screen_text_70_color" android:id="@id/route_guide_raster_in_out_txt" android:layout_toRightOf="@id/route_guide_raster_image_turn_dis_unit" android:layout_centerVertical="true" style="@style/DisplayNaviSimpleGuideMiniIntoTv" />
            <com.example.mxextend.widget.textview.NoSpaceTextView android:textSize="@dimen/tp_28" android:textColor="@color/full_screen_text_color" android:ellipsize="end" android:id="@id/route_guide_raster_image_road_name" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/tp_11" android:text="民族一条街" android:maxLines="1" android:layout_toRightOf="@id/route_guide_raster_in_out_txt" android:layout_centerVertical="true" android:layout_alignParentEnd="true" />
        </RelativeLayout>
        <LinearLayout android:gravity="center_vertical" android:id="@id/route_guide_raster_image_next_crossing_wrapper" android:background="@color/route_guide_raster_image_next_crossing_bkg" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="@dimen/tp_56" android:layout_below="@id/route_guide_raster_image_progressbar_info">
            <ImageView android:id="@id/route_guide_raster_image_next_crossing_icon" style="@style/RouteGuideRasterNextCrossingIcon" />
            <TextView android:id="@id/route_guide_raster_image_next_crossing_distance" style="@style/RouteGuideRasterNextCrossingDistance" />
            <TextView android:id="@id/route_guide_raster_next_crossing_distance_unit" android:layout_marginEnd="@dimen/tp_22" style="@style/RouteGuideRasterNextCrossingDistance" />
        </LinearLayout>
        <LinearLayout android:orientation="vertical" android:id="@id/route_guide_raster_load" android:background="@drawable/diff_bg_route_guide_progerss_bottom" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/tp_316" android:layout_below="@id/route_guide_raster_image_progressbar_info">
            <com.example.mxextend.widget.DiffDisPlayLaneInfoView android:id="@id/route_raster_lane_info" android:visibility="gone" android:layout_marginStart="@dimen/tp_28" style="@style/RouteGuideLaneLayoutView" />
            <com.example.mxextend.widget.RouteGuideETAView android:id="@id/cross_eta_view" android:background="@drawable/diff_bg_route_guide_simple_panel_bottom" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignParentBottom="true" />
        </LinearLayout>
    </RelativeLayout>
    <com.example.mxextend.widget.TMCLightBarView android:id="@id/route_guide_tmc_bar" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="@dimen/tp_496" android:layout_marginTop="@dimen/tp_80" android:layout_alignParentEnd="true" />
</RelativeLayout>