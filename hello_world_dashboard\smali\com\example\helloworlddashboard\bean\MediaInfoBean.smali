.class public Lcom/example/helloworlddashboard/bean/MediaInfoBean;
.super Ljava/lang/Object;
.source "MediaInfoBean.java"


# static fields
.field private static final TAG:Ljava/lang/String; = "MediaInfoBean"


# instance fields
.field public albumUrl:Ljava/lang/String;

.field public artist:Ljava/lang/String;

.field public currentPosition:I

.field public duration:I

.field private lastMediaType:I

.field private lastPlayState:I

.field public mediaType:I

.field public playState:I

.field public sourceId:Ljava/lang/String;

.field public title:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 17
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    .line 24
    iput v0, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->lastMediaType:I

    .line 28
    iput v0, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->lastPlayState:I

    .line 38
    iput v0, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->mediaType:I

    return-void
.end method


# virtual methods
.method public canUpdateMediaInfo(II)Z
    .locals 2

    .line 80
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "canUpdateMediaInfo mediaType:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " , playState:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " , lastMediaType:"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget v0, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->lastMediaType:I

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " , lastPlayState:"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget v0, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->lastPlayState:I

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p2

    const-string v0, " , sourceId:"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    iget-object v0, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->sourceId:Ljava/lang/String;

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const-string v0, "MediaInfoBean"

    invoke-static {v0, p2}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 81
    iget-object p2, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->sourceId:Ljava/lang/String;

    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p2

    if-eqz p2, :cond_0

    const/4 p1, 0x1

    return p1

    .line 84
    :cond_0
    iget-object p2, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->sourceId:Ljava/lang/String;

    invoke-static {p1}, Lcom/example/helloworlddashboard/utils/DataUtil;->mediaTypeToSourceId(I)Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    return p1
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    .line 139
    :cond_0
    instance-of v1, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 142
    :cond_1
    check-cast p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;

    .line 143
    iget v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->lastMediaType:I

    iget v3, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->lastMediaType:I

    if-ne v1, v3, :cond_2

    iget v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->lastPlayState:I

    iget v3, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->lastPlayState:I

    if-ne v1, v3, :cond_2

    iget v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->mediaType:I

    iget v3, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->mediaType:I

    if-ne v1, v3, :cond_2

    iget v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->playState:I

    iget v3, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->playState:I

    if-ne v1, v3, :cond_2

    iget v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->duration:I

    iget v3, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->duration:I

    if-ne v1, v3, :cond_2

    iget v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->currentPosition:I

    iget v3, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->currentPosition:I

    if-ne v1, v3, :cond_2

    iget-object v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->title:Ljava/lang/String;

    iget-object v3, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->title:Ljava/lang/String;

    .line 149
    invoke-static {v1, v3}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->artist:Ljava/lang/String;

    iget-object v3, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->artist:Ljava/lang/String;

    .line 150
    invoke-static {v1, v3}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->albumUrl:Ljava/lang/String;

    iget-object p1, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->albumUrl:Ljava/lang/String;

    .line 151
    invoke-static {v1, p1}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public hashCode()I
    .locals 3

    const/16 v0, 0x9

    new-array v0, v0, [Ljava/lang/Object;

    .line 156
    iget v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->lastMediaType:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->lastPlayState:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    iget v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->mediaType:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    iget v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->playState:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x3

    aput-object v1, v0, v2

    iget-object v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->title:Ljava/lang/String;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    iget-object v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->artist:Ljava/lang/String;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    iget-object v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->albumUrl:Ljava/lang/String;

    const/4 v2, 0x6

    aput-object v1, v0, v2

    iget v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->duration:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x7

    aput-object v1, v0, v2

    iget v1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->currentPosition:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/16 v2, 0x8

    aput-object v1, v0, v2

    invoke-static {v0}, Ljava/util/Objects;->hash([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public needUpdate(IILjava/lang/String;Ljava/lang/String;)Z
    .locals 2

    .line 169
    iget v0, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->mediaType:I

    const/4 v1, 0x1

    if-ne p1, v0, :cond_0

    iget p1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->playState:I

    if-ne p2, p1, :cond_0

    iget-object p1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->title:Ljava/lang/String;

    .line 171
    invoke-static {p3, p1}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->artist:Ljava/lang/String;

    .line 172
    invoke-static {p4, p1}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_0

    move p1, v1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    xor-int/2addr p1, v1

    .line 174
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "needUpdate result:"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const-string p3, "MediaInfoBean"

    invoke-static {p3, p2}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return p1
.end method

.method public update(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;II)V
    .locals 1

    .line 110
    invoke-virtual {p0, p1, p2}, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->canUpdateMediaInfo(II)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 111
    iput p1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->mediaType:I

    .line 112
    iput p2, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->playState:I

    .line 113
    iput-object p3, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->title:Ljava/lang/String;

    .line 114
    iput-object p4, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->artist:Ljava/lang/String;

    .line 115
    iput-object p5, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->albumUrl:Ljava/lang/String;

    .line 116
    iput p6, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->duration:I

    .line 117
    iput p7, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->currentPosition:I

    :cond_0
    return-void
.end method

.method public update(Lcom/example/helloworlddashboard/bean/MediaInfoBean;)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    .line 125
    :cond_0
    iget v0, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->mediaType:I

    iput v0, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->mediaType:I

    .line 126
    iget v0, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->mediaType:I

    iput v0, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->playState:I

    .line 127
    iget-object v0, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->title:Ljava/lang/String;

    iput-object v0, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->title:Ljava/lang/String;

    .line 128
    iget-object v0, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->artist:Ljava/lang/String;

    iput-object v0, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->artist:Ljava/lang/String;

    .line 129
    iget-object v0, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->albumUrl:Ljava/lang/String;

    iput-object v0, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->albumUrl:Ljava/lang/String;

    .line 130
    iget v0, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->duration:I

    iput v0, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->duration:I

    .line 131
    iget p1, p1, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->currentPosition:I

    iput p1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->currentPosition:I

    return-void
.end method

.method public updateSourceId(Ljava/lang/String;)V
    .locals 2

    .line 68
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "updateSourceId sourceId:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "MediaInfoBean"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 69
    iput-object p1, p0, Lcom/example/helloworlddashboard/bean/MediaInfoBean;->sourceId:Ljava/lang/String;

    return-void
.end method
