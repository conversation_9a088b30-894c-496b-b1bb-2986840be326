.class Lcom/example/helloworlddashboard/MainActivity$2;
.super Ljava/lang/Object;
.source "MainActivity.java"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/example/helloworlddashboard/MainActivity;->initViews()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/example/helloworlddashboard/MainActivity;


# direct methods
.method constructor <init>(Lcom/example/helloworlddashboard/MainActivity;)V
    .locals 0

    .line 57
    iput-object p1, p0, Lcom/example/helloworlddashboard/MainActivity$2;->this$0:Lcom/example/helloworlddashboard/MainActivity;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 2

    .line 60
    const-string p1, "MainActivity"

    const-string v0, "Full Navigation OFF clicked"

    invoke-static {p1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 61
    iget-object p1, p0, Lcom/example/helloworlddashboard/MainActivity$2;->this$0:Lcom/example/helloworlddashboard/MainActivity;

    invoke-virtual {p1}, Lcom/example/helloworlddashboard/MainActivity;->getCarServiceModel()Lcom/example/helloworlddashboard/model/CarServiceModel;

    move-result-object p1

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->requestMeterFullNavi(Z)V

    return-void
.end method
.end class
