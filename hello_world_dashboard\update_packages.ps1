# PowerShell script to update package names in smali files
$oldPackage = "com/chinatsp/dashboard"
$newPackage = "com/example/helloworlddashboard"

# Get all smali files
$smaliFiles = Get-ChildItem -Path "smali" -Filter "*.smali" -Recurse

foreach ($file in $smaliFiles) {
    Write-Host "Processing: $($file.FullName)"
    
    # Read file content
    $content = Get-Content $file.FullName -Raw
    
    # Replace package references
    $content = $content -replace "Lcom/chinatsp/dashboard", "Lcom/example/helloworlddashboard"
    $content = $content -replace "com/chinatsp/dashboard", "com/example/helloworlddashboard"
    $content = $content -replace "com\.chinatsp\.dashboard", "com.example.helloworlddashboard"
    
    # Write back to file
    Set-Content -Path $file.FullName -Value $content -NoNewline
}

Write-Host "Package update completed!"
