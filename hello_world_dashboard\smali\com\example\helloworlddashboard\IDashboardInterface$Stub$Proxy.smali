.class Lcom/example/helloworlddashboard/IDashboardInterface$Stub$Proxy;
.super Ljava/lang/Object;
.source "IDashboardInterface.java"

# interfaces
.implements Lcom/example/helloworlddashboard/IDashboardInterface;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/example/helloworlddashboard/IDashboardInterface$Stub;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "Proxy"
.end annotation


# static fields
.field public static sDefaultImpl:Lcom/example/helloworlddashboard/IDashboardInterface;


# instance fields
.field private mRemote:Landroid/os/IBinder;


# direct methods
.method constructor <init>(Landroid/os/IBinder;)V
    .locals 0

    .line 169
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 170
    iput-object p1, p0, Lcom/example/helloworlddashboard/IDashboardInterface$Stub$Proxy;->mRemote:Landroid/os/IBinder;

    return-void
.end method


# virtual methods
.method public asBinder()Landroid/os/IBinder;
    .locals 1

    .line 174
    iget-object v0, p0, Lcom/example/helloworlddashboard/IDashboardInterface$Stub$Proxy;->mRemote:Landroid/os/IBinder;

    return-object v0
.end method

.method public getInterfaceDescriptor()Ljava/lang/String;
    .locals 1

    const-string v0, "com.example.helloworlddashboard.IDashboardInterface"

    return-object v0
.end method

.method public onReceiveMediaSource(Ljava/lang/String;)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroid/os/RemoteException;
        }
    .end annotation

    .line 186
    invoke-static {}, Landroid/os/Parcel;->obtain()Landroid/os/Parcel;

    move-result-object v0

    .line 187
    invoke-static {}, Landroid/os/Parcel;->obtain()Landroid/os/Parcel;

    move-result-object v1

    :try_start_0
    const-string v2, "com.example.helloworlddashboard.IDashboardInterface"

    .line 189
    invoke-virtual {v0, v2}, Landroid/os/Parcel;->writeInterfaceToken(Ljava/lang/String;)V

    .line 190
    invoke-virtual {v0, p1}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    .line 191
    iget-object v2, p0, Lcom/example/helloworlddashboard/IDashboardInterface$Stub$Proxy;->mRemote:Landroid/os/IBinder;

    const/4 v3, 0x1

    const/4 v4, 0x0

    invoke-interface {v2, v3, v0, v1, v4}, Landroid/os/IBinder;->transact(ILandroid/os/Parcel;Landroid/os/Parcel;I)Z

    move-result v2

    if-nez v2, :cond_0

    .line 192
    invoke-static {}, Lcom/example/helloworlddashboard/IDashboardInterface$Stub;->getDefaultImpl()Lcom/example/helloworlddashboard/IDashboardInterface;

    move-result-object v2

    if-eqz v2, :cond_0

    .line 193
    invoke-static {}, Lcom/example/helloworlddashboard/IDashboardInterface$Stub;->getDefaultImpl()Lcom/example/helloworlddashboard/IDashboardInterface;

    move-result-object v2

    invoke-interface {v2, p1}, Lcom/example/helloworlddashboard/IDashboardInterface;->onReceiveMediaSource(Ljava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 199
    invoke-virtual {v1}, Landroid/os/Parcel;->recycle()V

    .line 200
    invoke-virtual {v0}, Landroid/os/Parcel;->recycle()V

    return-void

    .line 196
    :cond_0
    :try_start_1
    invoke-virtual {v1}, Landroid/os/Parcel;->readException()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 199
    invoke-virtual {v1}, Landroid/os/Parcel;->recycle()V

    .line 200
    invoke-virtual {v0}, Landroid/os/Parcel;->recycle()V

    return-void

    :catchall_0
    move-exception p1

    .line 199
    invoke-virtual {v1}, Landroid/os/Parcel;->recycle()V

    .line 200
    invoke-virtual {v0}, Landroid/os/Parcel;->recycle()V

    .line 201
    throw p1
.end method

.method public onReceivedMediaInfo(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;II)V
    .locals 13
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroid/os/RemoteException;
        }
    .end annotation

    .line 215
    invoke-static {}, Landroid/os/Parcel;->obtain()Landroid/os/Parcel;

    move-result-object v1

    .line 216
    invoke-static {}, Landroid/os/Parcel;->obtain()Landroid/os/Parcel;

    move-result-object v2

    :try_start_0
    const-string v0, "com.example.helloworlddashboard.IDashboardInterface"

    .line 218
    invoke-virtual {v1, v0}, Landroid/os/Parcel;->writeInterfaceToken(Ljava/lang/String;)V

    move v0, p1

    .line 219
    invoke-virtual {v1, p1}, Landroid/os/Parcel;->writeInt(I)V

    move v5, p2

    .line 220
    invoke-virtual {v1, p2}, Landroid/os/Parcel;->writeInt(I)V

    move-object/from16 v6, p3

    .line 221
    invoke-virtual {v1, v6}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    move-object/from16 v7, p4

    .line 222
    invoke-virtual {v1, v7}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    move-object/from16 v8, p5

    .line 223
    invoke-virtual {v1, v8}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    move/from16 v9, p6

    .line 224
    invoke-virtual {v1, v9}, Landroid/os/Parcel;->writeInt(I)V

    move/from16 v10, p7

    .line 225
    invoke-virtual {v1, v10}, Landroid/os/Parcel;->writeInt(I)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    move-object v11, p0

    .line 226
    :try_start_1
    iget-object v3, v11, Lcom/example/helloworlddashboard/IDashboardInterface$Stub$Proxy;->mRemote:Landroid/os/IBinder;

    const/4 v4, 0x2

    const/4 v12, 0x0

    invoke-interface {v3, v4, v1, v2, v12}, Landroid/os/IBinder;->transact(ILandroid/os/Parcel;Landroid/os/Parcel;I)Z

    move-result v3

    if-nez v3, :cond_0

    .line 227
    invoke-static {}, Lcom/example/helloworlddashboard/IDashboardInterface$Stub;->getDefaultImpl()Lcom/example/helloworlddashboard/IDashboardInterface;

    move-result-object v3

    if-eqz v3, :cond_0

    .line 228
    invoke-static {}, Lcom/example/helloworlddashboard/IDashboardInterface$Stub;->getDefaultImpl()Lcom/example/helloworlddashboard/IDashboardInterface;

    move-result-object v3

    move v4, p1

    move v5, p2

    move-object/from16 v6, p3

    move-object/from16 v7, p4

    move-object/from16 v8, p5

    move/from16 v9, p6

    move/from16 v10, p7

    invoke-interface/range {v3 .. v10}, Lcom/example/helloworlddashboard/IDashboardInterface;->onReceivedMediaInfo(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;II)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 234
    invoke-virtual {v2}, Landroid/os/Parcel;->recycle()V

    .line 235
    invoke-virtual {v1}, Landroid/os/Parcel;->recycle()V

    return-void

    .line 231
    :cond_0
    :try_start_2
    invoke-virtual {v2}, Landroid/os/Parcel;->readException()V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 234
    invoke-virtual {v2}, Landroid/os/Parcel;->recycle()V

    .line 235
    invoke-virtual {v1}, Landroid/os/Parcel;->recycle()V

    return-void

    :catchall_0
    move-exception v0

    goto :goto_0

    :catchall_1
    move-exception v0

    move-object v11, p0

    .line 234
    :goto_0
    invoke-virtual {v2}, Landroid/os/Parcel;->recycle()V

    .line 235
    invoke-virtual {v1}, Landroid/os/Parcel;->recycle()V

    .line 236
    throw v0
.end method

.method public requestFitnessAnim(IZZZZIIIIIILjava/lang/String;Ljava/lang/String;)Z
    .locals 17
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroid/os/RemoteException;
        }
    .end annotation

    .line 256
    invoke-static {}, Landroid/os/Parcel;->obtain()Landroid/os/Parcel;

    move-result-object v1

    .line 257
    invoke-static {}, Landroid/os/Parcel;->obtain()Landroid/os/Parcel;

    move-result-object v2

    :try_start_0
    const-string v0, "com.example.helloworlddashboard.IDashboardInterface"

    .line 260
    invoke-virtual {v1, v0}, Landroid/os/Parcel;->writeInterfaceToken(Ljava/lang/String;)V

    move/from16 v0, p1

    .line 261
    invoke-virtual {v1, v0}, Landroid/os/Parcel;->writeInt(I)V

    const/4 v3, 0x1

    const/4 v4, 0x0

    if-eqz p2, :cond_0

    move v5, v3

    goto :goto_0

    :cond_0
    move v5, v4

    .line 262
    :goto_0
    invoke-virtual {v1, v5}, Landroid/os/Parcel;->writeInt(I)V

    if-eqz p3, :cond_1

    move v5, v3

    goto :goto_1

    :cond_1
    move v5, v4

    .line 263
    :goto_1
    invoke-virtual {v1, v5}, Landroid/os/Parcel;->writeInt(I)V

    if-eqz p4, :cond_2

    move v5, v3

    goto :goto_2

    :cond_2
    move v5, v4

    .line 264
    :goto_2
    invoke-virtual {v1, v5}, Landroid/os/Parcel;->writeInt(I)V

    if-eqz p5, :cond_3

    move v5, v3

    goto :goto_3

    :cond_3
    move v5, v4

    .line 265
    :goto_3
    invoke-virtual {v1, v5}, Landroid/os/Parcel;->writeInt(I)V

    move/from16 v9, p6

    .line 266
    invoke-virtual {v1, v9}, Landroid/os/Parcel;->writeInt(I)V

    move/from16 v10, p7

    .line 267
    invoke-virtual {v1, v10}, Landroid/os/Parcel;->writeInt(I)V

    move/from16 v11, p8

    .line 268
    invoke-virtual {v1, v11}, Landroid/os/Parcel;->writeInt(I)V

    move/from16 v12, p9

    .line 269
    invoke-virtual {v1, v12}, Landroid/os/Parcel;->writeInt(I)V

    move/from16 v13, p10

    .line 270
    invoke-virtual {v1, v13}, Landroid/os/Parcel;->writeInt(I)V

    move/from16 v14, p11

    .line 271
    invoke-virtual {v1, v14}, Landroid/os/Parcel;->writeInt(I)V

    move-object/from16 v15, p12

    .line 272
    invoke-virtual {v1, v15}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    move-object/from16 v8, p13

    .line 273
    invoke-virtual {v1, v8}, Landroid/os/Parcel;->writeString(Ljava/lang/String;)V

    move-object/from16 v7, p0

    .line 274
    iget-object v5, v7, Lcom/example/helloworlddashboard/IDashboardInterface$Stub$Proxy;->mRemote:Landroid/os/IBinder;

    const/4 v6, 0x3

    invoke-interface {v5, v6, v1, v2, v4}, Landroid/os/IBinder;->transact(ILandroid/os/Parcel;Landroid/os/Parcel;I)Z

    move-result v5

    if-nez v5, :cond_4

    .line 275
    invoke-static {}, Lcom/example/helloworlddashboard/IDashboardInterface$Stub;->getDefaultImpl()Lcom/example/helloworlddashboard/IDashboardInterface;

    move-result-object v5

    if-eqz v5, :cond_4

    .line 276
    invoke-static {}, Lcom/example/helloworlddashboard/IDashboardInterface$Stub;->getDefaultImpl()Lcom/example/helloworlddashboard/IDashboardInterface;

    move-result-object v3

    move/from16 v4, p1

    move/from16 v5, p2

    move/from16 v6, p3

    move/from16 v7, p4

    move/from16 v8, p5

    move/from16 v9, p6

    move/from16 v10, p7

    move/from16 v11, p8

    move/from16 v12, p9

    move/from16 v13, p10

    move/from16 v14, p11

    move-object/from16 v15, p12

    move-object/from16 v16, p13

    invoke-interface/range {v3 .. v16}, Lcom/example/helloworlddashboard/IDashboardInterface;->requestFitnessAnim(IZZZZIIIIIILjava/lang/String;Ljava/lang/String;)Z

    move-result v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 282
    invoke-virtual {v2}, Landroid/os/Parcel;->recycle()V

    .line 283
    invoke-virtual {v1}, Landroid/os/Parcel;->recycle()V

    return v0

    .line 278
    :cond_4
    :try_start_1
    invoke-virtual {v2}, Landroid/os/Parcel;->readException()V

    .line 279
    invoke-virtual {v2}, Landroid/os/Parcel;->readInt()I

    move-result v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-eqz v0, :cond_5

    goto :goto_4

    :cond_5
    move v3, v4

    .line 282
    :goto_4
    invoke-virtual {v2}, Landroid/os/Parcel;->recycle()V

    .line 283
    invoke-virtual {v1}, Landroid/os/Parcel;->recycle()V

    return v3

    :catchall_0
    move-exception v0

    .line 282
    invoke-virtual {v2}, Landroid/os/Parcel;->recycle()V

    .line 283
    invoke-virtual {v1}, Landroid/os/Parcel;->recycle()V

    .line 284
    throw v0
.end method
