<?xml version="1.0" encoding="utf-8"?>
<merge
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:orientation="horizontal" android:paddingTop="@dimen/tp_96" android:layout_width="fill_parent" android:layout_height="@dimen/tp_160">
        <TextView android:layout_gravity="center" android:id="@id/txt_type" android:text="@string/route_guide_highway_tollgate" style="@style/HighwayPanelTypeText" />
        <View android:layout_width="wrap_content" android:layout_height="@dimen/tp_0" android:layout_weight="1.0" />
        <TextView android:layout_gravity="center" android:id="@id/txt_distance" style="@style/HighwayPanelDistanceText" />
        <TextView android:layout_gravity="center" android:id="@id/txt_unit" style="@style/HighwayPanelUnitText" />
    </LinearLayout>
</merge>