<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="horizontal" android:id="@id/month_navigation_bar" android:paddingTop="@dimen/mtrl_calendar_navigation_top_padding" android:paddingBottom="@dimen/mtrl_calendar_navigation_bottom_padding" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0">
        <com.google.android.material.button.MaterialButton android:gravity="start|center" android:layout_gravity="start" android:id="@id/month_navigation_fragment_toggle" android:layout_width="wrap_content" android:layout_height="@dimen/mtrl_calendar_navigation_height" android:insetTop="0.0dip" android:insetBottom="0.0dip" app:icon="@drawable/material_ic_menu_arrow_down_black_24dp" app:iconGravity="textEnd" app:iconPadding="4.0dip" style="?materialCalendarYearNavigationButton" />
    </FrameLayout>
    <com.google.android.material.button.MaterialButton android:gravity="center" android:id="@id/month_navigation_previous" android:layout_width="@dimen/mtrl_min_touch_target_size" android:layout_height="@dimen/mtrl_calendar_navigation_height" android:insetTop="0.0dip" android:insetBottom="0.0dip" android:contentDescription="@string/mtrl_picker_a11y_prev_month" app:icon="@drawable/material_ic_keyboard_arrow_previous_black_24dp" style="?materialCalendarMonthNavigationButton" />
    <com.google.android.material.button.MaterialButton android:gravity="center" android:id="@id/month_navigation_next" android:layout_width="@dimen/mtrl_min_touch_target_size" android:layout_height="@dimen/mtrl_calendar_navigation_height" android:insetTop="0.0dip" android:insetBottom="0.0dip" android:contentDescription="@string/mtrl_picker_a11y_next_month" app:icon="@drawable/material_ic_keyboard_arrow_next_black_24dp" style="?materialCalendarMonthNavigationButton" />
</LinearLayout>