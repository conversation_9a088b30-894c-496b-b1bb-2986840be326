<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_gravity="center" android:id="@id/material_clock_face" android:layout_width="256.0dip" android:layout_height="256.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.timepicker.ClockHandView android:id="@id/material_clock_hand" android:tag="skip" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:selectorSize="24.0dip" />
</merge>