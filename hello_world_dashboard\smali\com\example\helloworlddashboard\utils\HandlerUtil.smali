.class public Lcom/example/helloworlddashboard/utils/HandlerUtil;
.super Ljava/lang/Object;
.source "HandlerUtil.java"


# static fields
.field private static volatile mainHandler:Landroid/os/Handler;

.field private static volatile threadHandler:Landroid/os/Handler;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 12
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static getMainHandler()Landroid/os/Handler;
    .locals 3

    .line 22
    sget-object v0, Lcom/example/helloworlddashboard/utils/HandlerUtil;->mainHandler:Landroid/os/Handler;

    if-nez v0, :cond_1

    .line 23
    const-class v0, Lcom/example/helloworlddashboard/utils/HandlerUtil;

    monitor-enter v0

    .line 24
    :try_start_0
    sget-object v1, Lcom/example/helloworlddashboard/utils/HandlerUtil;->mainHandler:Landroid/os/Handler;

    if-nez v1, :cond_0

    .line 25
    new-instance v1, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v2

    invoke-direct {v1, v2}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    sput-object v1, Lcom/example/helloworlddashboard/utils/HandlerUtil;->mainHandler:Landroid/os/Handler;

    .line 27
    :cond_0
    monitor-exit v0

    goto :goto_0

    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1

    .line 29
    :cond_1
    :goto_0
    sget-object v0, Lcom/example/helloworlddashboard/utils/HandlerUtil;->mainHandler:Landroid/os/Handler;

    return-object v0
.end method

.method public static getThreadHandler()Landroid/os/Handler;
    .locals 3

    .line 37
    sget-object v0, Lcom/example/helloworlddashboard/utils/HandlerUtil;->threadHandler:Landroid/os/Handler;

    if-nez v0, :cond_1

    .line 38
    const-class v0, Lcom/example/helloworlddashboard/utils/HandlerUtil;

    monitor-enter v0

    .line 39
    :try_start_0
    sget-object v1, Lcom/example/helloworlddashboard/utils/HandlerUtil;->threadHandler:Landroid/os/Handler;

    if-nez v1, :cond_0

    .line 40
    new-instance v1, Landroid/os/HandlerThread;

    const-string v2, "dashboard-async"

    invoke-direct {v1, v2}, Landroid/os/HandlerThread;-><init>(Ljava/lang/String;)V

    .line 41
    invoke-virtual {v1}, Landroid/os/HandlerThread;->start()V

    .line 42
    new-instance v2, Landroid/os/Handler;

    invoke-virtual {v1}, Landroid/os/HandlerThread;->getLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v2, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    sput-object v2, Lcom/example/helloworlddashboard/utils/HandlerUtil;->threadHandler:Landroid/os/Handler;

    .line 44
    :cond_0
    monitor-exit v0

    goto :goto_0

    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1

    .line 46
    :cond_1
    :goto_0
    sget-object v0, Lcom/example/helloworlddashboard/utils/HandlerUtil;->threadHandler:Landroid/os/Handler;

    return-object v0
.end method
