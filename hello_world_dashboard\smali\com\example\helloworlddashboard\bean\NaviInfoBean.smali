.class public Lcom/example/helloworlddashboard/bean/NaviInfoBean;
.super Ljava/lang/Object;
.source "NaviInfoBean.java"


# instance fields
.field private curDis:I

.field private huNaviIconNum:I

.field private nextRoadName:Ljava/lang/String;

.field private totalDis:I

.field private totalTime:I


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 11
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public clear()V
    .locals 2

    const/4 v0, 0x0

    .line 112
    iput v0, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->totalDis:I

    .line 113
    iput v0, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->totalTime:I

    .line 114
    iput v0, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->curDis:I

    const-string v1, ""

    .line 115
    iput-object v1, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->nextRoadName:Ljava/lang/String;

    .line 116
    iput v0, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->huNaviIconNum:I

    return-void
.end method

.method public getCurDis()I
    .locals 1

    .line 50
    iget v0, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->curDis:I

    return v0
.end method

.method public getHuNaviIconNum()I
    .locals 1

    .line 66
    iget v0, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->huNaviIconNum:I

    return v0
.end method

.method public getNextRoadName()Ljava/lang/String;
    .locals 1

    .line 58
    iget-object v0, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->nextRoadName:Ljava/lang/String;

    return-object v0
.end method

.method public getTotalDis()I
    .locals 1

    .line 34
    iget v0, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->totalDis:I

    return v0
.end method

.method public getTotalTime()I
    .locals 1

    .line 42
    iget v0, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->totalTime:I

    return v0
.end method

.method public needUpdate(IIILjava/lang/String;I)Z
    .locals 1

    .line 84
    iget v0, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->totalDis:I

    if-ne v0, p1, :cond_1

    iget p1, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->totalTime:I

    if-ne p1, p2, :cond_1

    iget p1, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->curDis:I

    if-ne p1, p3, :cond_1

    iget-object p1, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->nextRoadName:Ljava/lang/String;

    .line 87
    invoke-static {p1, p4}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_1

    iget p1, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->huNaviIconNum:I

    if-eq p1, p5, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method public setCurDis(I)V
    .locals 0

    .line 54
    iput p1, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->curDis:I

    return-void
.end method

.method public setHuNaviIconNum(I)V
    .locals 0

    .line 70
    iput p1, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->huNaviIconNum:I

    return-void
.end method

.method public setNextRoadName(Ljava/lang/String;)V
    .locals 0

    .line 62
    iput-object p1, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->nextRoadName:Ljava/lang/String;

    return-void
.end method

.method public setTotalDis(I)V
    .locals 0

    .line 38
    iput p1, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->totalDis:I

    return-void
.end method

.method public setTotalTime(I)V
    .locals 0

    .line 46
    iput p1, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->totalTime:I

    return-void
.end method

.method public update(IIILjava/lang/String;I)V
    .locals 0

    .line 101
    iput p1, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->totalDis:I

    .line 102
    iput p2, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->totalTime:I

    .line 103
    iput p3, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->curDis:I

    .line 104
    iput-object p4, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->nextRoadName:Ljava/lang/String;

    .line 105
    iput p5, p0, Lcom/example/helloworlddashboard/bean/NaviInfoBean;->huNaviIconNum:I

    return-void
.end method
