<?xml version="1.0" encoding="utf-8"?>
<CheckedTextView android:textAppearance="?android:textAppearanceMedium" android:textColor="?textColorAlertDialogListItem" android:ellipsize="marquee" android:gravity="center_vertical" android:id="@android:id/text1" android:paddingLeft="@dimen/abc_select_dialog_padding_start_material" android:paddingRight="?dialogPreferredPadding" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="?listPreferredItemHeightSmall" android:drawableLeft="?android:listChoiceIndicatorSingle" android:drawablePadding="20.0dip" android:drawableStart="?android:listChoiceIndicatorSingle" android:paddingStart="@dimen/abc_select_dialog_padding_start_material" android:paddingEnd="?dialogPreferredPadding"
  xmlns:android="http://schemas.android.com/apk/res/android" />