<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <Button android:id="@id/share_mileage" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="100.0px" android:text="里程分享" android:layout_marginStart="100.0px" />
    <Button android:id="@id/share_oil_consumption" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="油耗分享" app:layout_constraintTop_toBottomOf="@id/share_mileage" />
    <Button android:id="@id/startService" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="启动服务" app:layout_constraintTop_toBottomOf="@id/share_oil_consumption" />
</androidx.constraintlayout.widget.ConstraintLayout>