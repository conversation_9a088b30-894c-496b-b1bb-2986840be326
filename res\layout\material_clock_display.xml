<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="horizontal" android:id="@id/material_clock_display" android:background="?colorSurface" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:baselineAligned="false" android:layoutDirection="ltr" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <include android:id="@id/material_hour_tv" layout="@layout/material_time_chip" />
    <include layout="@layout/material_clock_display_divider" />
    <include android:id="@id/material_minute_tv" layout="@layout/material_time_chip" />
</LinearLayout>