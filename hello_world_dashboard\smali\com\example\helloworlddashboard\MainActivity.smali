.class public Lcom/example/helloworlddashboard/MainActivity;
.super Landroid/app/Activity;
.source "MainActivity.java"

# interfaces
.implements Lcom/example/helloworlddashboard/callback/CarServiceCallback;


# instance fields
.field private dashboardService:Lcom/example/helloworlddashboard/DashboardService;

.field private carServiceModel:Lcom/example/helloworlddashboard/model/CarServiceModel;

.field private btnFullNaviOn:Landroid/widget/Button;

.field private btnFullNaviOff:Landroid/widget/Button;

.field private btnSimpleNaviOn:Landroid/widget/Button;

.field private btnSimpleNaviOff:Landroid/widget/Button;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 15
    invoke-direct {p0}, Landroid/app/Activity;-><init>()V

    return-void
.end method

.method private initViews()V
    .locals 2

    .line 45
    const v0, 0x7f080001    # @+id/btn_full_navi_on

    invoke-virtual {p0, v0}, Lcom/example/helloworlddashboard/MainActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/Button;

    iput-object v0, p0, Lcom/example/helloworlddashboard/MainActivity;->btnFullNaviOn:Landroid/widget/Button;

    .line 46
    const v0, 0x7f080002    # @+id/btn_full_navi_off

    invoke-virtual {p0, v0}, Lcom/example/helloworlddashboard/MainActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/Button;

    iput-object v0, p0, Lcom/example/helloworlddashboard/MainActivity;->btnFullNaviOff:Landroid/widget/Button;

    .line 47
    const v0, 0x7f080003    # @+id/btn_simple_navi_on

    invoke-virtual {p0, v0}, Lcom/example/helloworlddashboard/MainActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/Button;

    iput-object v0, p0, Lcom/example/helloworlddashboard/MainActivity;->btnSimpleNaviOn:Landroid/widget/Button;

    .line 48
    const v0, 0x7f080004    # @+id/btn_simple_navi_off

    invoke-virtual {p0, v0}, Lcom/example/helloworlddashboard/MainActivity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/Button;

    iput-object v0, p0, Lcom/example/helloworlddashboard/MainActivity;->btnSimpleNaviOff:Landroid/widget/Button;

    .line 50
    iget-object v0, p0, Lcom/example/helloworlddashboard/MainActivity;->btnFullNaviOn:Landroid/widget/Button;

    new-instance v1, Lcom/example/helloworlddashboard/MainActivity$1;

    invoke-direct {v1, p0}, Lcom/example/helloworlddashboard/MainActivity$1;-><init>(Lcom/example/helloworlddashboard/MainActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 57
    iget-object v0, p0, Lcom/example/helloworlddashboard/MainActivity;->btnFullNaviOff:Landroid/widget/Button;

    new-instance v1, Lcom/example/helloworlddashboard/MainActivity$2;

    invoke-direct {v1, p0}, Lcom/example/helloworlddashboard/MainActivity$2;-><init>(Lcom/example/helloworlddashboard/MainActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 64
    iget-object v0, p0, Lcom/example/helloworlddashboard/MainActivity;->btnSimpleNaviOn:Landroid/widget/Button;

    new-instance v1, Lcom/example/helloworlddashboard/MainActivity$3;

    invoke-direct {v1, p0}, Lcom/example/helloworlddashboard/MainActivity$3;-><init>(Lcom/example/helloworlddashboard/MainActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 71
    iget-object v0, p0, Lcom/example/helloworlddashboard/MainActivity;->btnSimpleNaviOff:Landroid/widget/Button;

    new-instance v1, Lcom/example/helloworlddashboard/MainActivity$4;

    invoke-direct {v1, p0}, Lcom/example/helloworlddashboard/MainActivity$4;-><init>(Lcom/example/helloworlddashboard/MainActivity;)V

    invoke-virtual {v0, v1}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    return-void
.end method


# virtual methods
.method public getCarServiceModel()Lcom/example/helloworlddashboard/model/CarServiceModel;
    .locals 1

    .line 85
    iget-object v0, p0, Lcom/example/helloworlddashboard/MainActivity;->carServiceModel:Lcom/example/helloworlddashboard/model/CarServiceModel;

    return-object v0
.end method

.method public onCarServiceConnected()V
    .locals 2

    .line 90
    const-string v0, "MainActivity"

    const-string v1, "Car service connected!"

    invoke-static {v0, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public onCarServiceDisconnected()V
    .locals 2

    .line 95
    const-string v0, "MainActivity"

    const-string v1, "Car service disconnected!"

    invoke-static {v0, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method protected onCreate(Landroid/os/Bundle;)V
    .locals 1

    .line 25
    invoke-super {p0, p1}, Landroid/app/Activity;->onCreate(Landroid/os/Bundle;)V

    .line 26
    const v0, 0x7f0b001c    # @layout/activity_main

    invoke-virtual {p0, v0}, Lcom/example/helloworlddashboard/MainActivity;->setContentView(I)V

    .line 28
    invoke-direct {p0}, Lcom/example/helloworlddashboard/MainActivity;->initViews()V

    .line 30
    new-instance p1, Lcom/example/helloworlddashboard/model/CarServiceModel;

    invoke-direct {p1, p0, p0}, Lcom/example/helloworlddashboard/model/CarServiceModel;-><init>(Landroid/content/Context;Lcom/example/helloworlddashboard/callback/CarServiceCallback;)V

    iput-object p1, p0, Lcom/example/helloworlddashboard/MainActivity;->carServiceModel:Lcom/example/helloworlddashboard/model/CarServiceModel;

    .line 31
    iget-object p1, p0, Lcom/example/helloworlddashboard/MainActivity;->carServiceModel:Lcom/example/helloworlddashboard/model/CarServiceModel;

    invoke-virtual {p1}, Lcom/example/helloworlddashboard/model/CarServiceModel;->register()V

    return-void
.end method

.method protected onDestroy()V
    .locals 1

    .line 36
    invoke-super {p0}, Landroid/app/Activity;->onDestroy()V

    .line 37
    iget-object v0, p0, Lcom/example/helloworlddashboard/MainActivity;->carServiceModel:Lcom/example/helloworlddashboard/model/CarServiceModel;

    if-eqz v0, :cond_0

    .line 38
    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/CarServiceModel;->unregister()V

    :cond_0
    return-void
.end method
.end class
