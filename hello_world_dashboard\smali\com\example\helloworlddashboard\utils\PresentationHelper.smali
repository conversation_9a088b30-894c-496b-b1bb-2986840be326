.class public Lcom/example/helloworlddashboard/utils/PresentationHelper;
.super Ljava/lang/Object;
.source "PresentationHelper.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;
    }
.end annotation


# static fields
.field private static final TAG:Ljava/lang/String; = "PresentationHelper"

.field private static mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

.field private static mKeepOnPresentation:Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;

.field private static mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

.field private static mSdDriverPresentation:Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

.field private static mSimpleMapPresentation:Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 20
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static ExecFitnessCmd(Landroid/content/Context;IZZZZIIIIIILjava/lang/String;Ljava/lang/String;)Z
    .locals 16

    .line 452
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isFitnessAnimShowing()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 453
    invoke-static {}, Lcom/example/fitness/DashboardFitnessAPI;->getInstance()Lcom/example/fitness/DashboardFitnessAPI;

    move-result-object v1

    move-object/from16 v2, p0

    move/from16 v3, p1

    move/from16 v4, p2

    move/from16 v5, p3

    move/from16 v6, p4

    move/from16 v7, p5

    move/from16 v8, p6

    move/from16 v9, p7

    move/from16 v10, p8

    move/from16 v11, p9

    move/from16 v12, p10

    move/from16 v13, p11

    move-object/from16 v14, p12

    move-object/from16 v15, p13

    invoke-virtual/range {v1 .. v15}, Lcom/example/fitness/DashboardFitnessAPI;->ExecCmd(Landroid/content/Context;IZZZZIIIIIILjava/lang/String;Ljava/lang/String;)V

    const/4 v0, 0x1

    return v0

    :cond_0
    const-string v0, "PresentationHelper"

    const-string v1, "Fitness Anim is not Showing "

    .line 456
    invoke-static {v0, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v0, 0x0

    return v0
.end method

.method public static declared-synchronized destroy()V
    .locals 4

    const-class v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;

    monitor-enter v0

    :try_start_0
    const-string v1, "PresentationHelper"

    const-string v2, "destroy"

    .line 544
    invoke-static {v1, v2}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 545
    sget-object v1, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    const/4 v2, 0x0

    if-eqz v1, :cond_0

    .line 546
    invoke-virtual {v1}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->dismiss()V

    .line 547
    sget-object v1, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    invoke-virtual {v1, v2}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->setOnShowListener(Landroid/content/DialogInterface$OnShowListener;)V

    .line 548
    sput-object v2, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    .line 550
    :cond_0
    sget-object v1, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSdDriverPresentation:Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

    if-eqz v1, :cond_1

    .line 551
    invoke-virtual {v1}, Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;->dismiss()V

    .line 552
    sget-object v1, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSdDriverPresentation:Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

    invoke-virtual {v1, v2}, Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;->setOnShowListener(Landroid/content/DialogInterface$OnShowListener;)V

    .line 553
    sput-object v2, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSdDriverPresentation:Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

    .line 555
    :cond_1
    sget-object v1, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    if-eqz v1, :cond_2

    const-string v1, "PresentationHelper"

    const-string v3, "set mapPresentation destroy"

    .line 556
    invoke-static {v1, v3}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 557
    sget-object v1, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    invoke-virtual {v1}, Lcom/example/helloworlddashboard/model/presentation/MapPresentation;->dismiss()V

    .line 558
    sget-object v1, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    invoke-virtual {v1, v2}, Lcom/example/helloworlddashboard/model/presentation/MapPresentation;->setOnShowListener(Landroid/content/DialogInterface$OnShowListener;)V

    .line 559
    sput-object v2, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    .line 561
    :cond_2
    sget-object v1, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSimpleMapPresentation:Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;

    if-eqz v1, :cond_3

    .line 562
    invoke-virtual {v1}, Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;->dismiss()V

    .line 563
    sget-object v1, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSimpleMapPresentation:Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;

    invoke-virtual {v1, v2}, Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;->setOnShowListener(Landroid/content/DialogInterface$OnShowListener;)V

    .line 564
    sput-object v2, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSimpleMapPresentation:Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 566
    :cond_3
    monitor-exit v0

    return-void

    :catchall_0
    move-exception v1

    monitor-exit v0

    throw v1
.end method

.method private static detectionAbnormal(Lcom/example/helloworlddashboard/model/presentation/PresentationBase;)Z
    .locals 1

    if-eqz p0, :cond_0

    .line 53
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/presentation/PresentationBase;->isShowing()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 54
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/presentation/PresentationBase;->haveAbnormal()Z

    move-result p0

    return p0

    :cond_0
    const/4 p0, 0x0

    return p0
.end method

.method public static dismissFitnessPresentation()V
    .locals 1

    .line 262
    invoke-static {}, Lcom/example/fitness/DashboardFitnessAPI;->getInstance()Lcom/example/fitness/DashboardFitnessAPI;

    move-result-object v0

    invoke-virtual {v0}, Lcom/example/fitness/DashboardFitnessAPI;->dismissFitnessPresentation()V

    return-void
.end method

.method public static dismissKeepOn()V
    .locals 2

    .line 518
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mKeepOnPresentation:Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;

    if-eqz v0, :cond_0

    .line 519
    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;->dismiss()V

    const/4 v0, 0x0

    .line 520
    sput-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mKeepOnPresentation:Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;

    :cond_0
    const-string v0, "PresentationHelper"

    const-string v1, "updateKeepOnMode dismissKeepOn"

    .line 522
    invoke-static {v0, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public static dismissMap()V
    .locals 2

    .line 503
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "set mapPresentation "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    sget-object v1, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "PresentationHelper"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 504
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    if-eqz v0, :cond_0

    .line 505
    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/MapPresentation;->dismiss()V

    const-string v0, "mMapPresentation dismissMap"

    .line 506
    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v0, 0x0

    .line 507
    sput-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    .line 508
    invoke-static {}, Ljava/lang/System;->gc()V

    :cond_0
    return-void
.end method

.method public static dismissPlayAim()V
    .locals 2

    .line 477
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    const-string v1, "PresentationHelper"

    if-eqz v0, :cond_0

    const-string v0, "dismissPlayAim will dismiss"

    .line 478
    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 479
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->isShowing()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 480
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->dismiss()V

    goto :goto_0

    :cond_0
    const-string v0, "dismissPlayAim fail. mBasePresentation is null."

    .line 483
    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    :cond_1
    :goto_0
    return-void
.end method

.method public static dismissSdPresentation()V
    .locals 2

    .line 465
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSdDriverPresentation:Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

    const-string v1, "PresentationHelper"

    if-eqz v0, :cond_0

    const-string v0, "dismissSdPresentation will dismiss"

    .line 466
    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 467
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSdDriverPresentation:Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;->dismiss()V

    goto :goto_0

    :cond_0
    const-string v0, "dismissSdPresentation. mSdDriverPresentation is null."

    .line 469
    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    :goto_0
    return-void
.end method

.method public static dismissSimpleMap()V
    .locals 2

    .line 491
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSimpleMapPresentation:Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;

    if-eqz v0, :cond_0

    .line 492
    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;->dismiss()V

    const/4 v0, 0x0

    .line 493
    sput-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSimpleMapPresentation:Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;

    .line 494
    invoke-static {}, Ljava/lang/System;->gc()V

    :cond_0
    const-string v0, "PresentationHelper"

    const-string v1, "dismissSimpleMap"

    .line 496
    invoke-static {v0, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public static getBasePresentation()Lcom/example/helloworlddashboard/model/presentation/BasePresentation;
    .locals 1

    .line 111
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    return-object v0
.end method

.method public static getBasePresentation(Landroid/content/Context;I)Lcom/example/helloworlddashboard/model/presentation/BasePresentation;
    .locals 1

    const/4 v0, 0x0

    .line 85
    invoke-static {p0, p1, v0}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->getBasePresentation(Landroid/content/Context;IZ)Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    move-result-object p0

    return-object p0
.end method

.method public static getBasePresentation(Landroid/content/Context;IZ)Lcom/example/helloworlddashboard/model/presentation/BasePresentation;
    .locals 2

    .line 90
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "getBasePresentation reCreate:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "PresentationHelper"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 91
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    if-eqz v0, :cond_0

    if-nez p2, :cond_0

    return-object v0

    .line 94
    :cond_0
    invoke-static {p0, p1}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->getDisplayByIndex(Landroid/content/Context;I)Landroid/view/Display;

    move-result-object p1

    if-nez p1, :cond_1

    const/4 p0, 0x0

    return-object p0

    :cond_1
    if-eqz p2, :cond_2

    .line 99
    sget-object p2, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    if-eqz p2, :cond_2

    .line 100
    invoke-virtual {p2}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->dismiss()V

    .line 103
    :cond_2
    new-instance p2, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    invoke-direct {p2, p0, p1}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;-><init>(Landroid/content/Context;Landroid/view/Display;)V

    sput-object p2, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    return-object p2
.end method

.method public static getCurrentAnimType()I
    .locals 5

    .line 431
    const-class v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;

    monitor-enter v0

    .line 432
    :try_start_0
    sget-object v1, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    .line 433
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v1, :cond_1

    .line 434
    invoke-virtual {v1}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->isShowing()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 438
    :cond_0
    invoke-virtual {v1}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->getCurrentAnimationType()I

    move-result v0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, -0x1

    const-string v2, "PresentationHelper"

    .line 436
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "getCurrentAnimType basePresentation:"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v2, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 440
    :goto_1
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isFitnessAnimShowing()Z

    move-result v1

    if-eqz v1, :cond_2

    const/16 v0, 0x15

    return v0

    :cond_2
    const-string v1, "PresentationHelper"

    .line 443
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getCurrentAnimType result:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v0

    :catchall_0
    move-exception v1

    .line 433
    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v1
.end method

.method public static getDisplayByIndex(Landroid/content/Context;I)Landroid/view/Display;
    .locals 6

    const-string v0, "display"

    .line 67
    invoke-virtual {p0, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Landroid/hardware/display/DisplayManager;

    .line 68
    invoke-virtual {p0}, Landroid/hardware/display/DisplayManager;->getDisplays()[Landroid/view/Display;

    move-result-object p0

    .line 69
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "A displays.length= "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    array-length v1, p0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "PresentationHelper"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 70
    array-length v0, p0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_0

    aget-object v3, p0, v2

    .line 71
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "A display getDisplayId= "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v3}, Landroid/view/Display;->getDisplayId()I

    move-result v3

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v1, v3}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    if-nez p0, :cond_1

    return-object v0

    .line 77
    :cond_1
    array-length v1, p0

    if-le v1, p1, :cond_2

    .line 78
    aget-object p0, p0, p1

    return-object p0

    :cond_2
    return-object v0
.end method

.method public static getSdDriverPresentation(Landroid/content/Context;I)Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;
    .locals 1

    .line 116
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSdDriverPresentation:Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

    if-eqz v0, :cond_0

    return-object v0

    .line 119
    :cond_0
    invoke-static {p0, p1}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->getDisplayByIndex(Landroid/content/Context;I)Landroid/view/Display;

    move-result-object p1

    if-nez p1, :cond_1

    const/4 p0, 0x0

    return-object p0

    .line 123
    :cond_1
    new-instance v0, Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

    invoke-direct {v0, p0, p1}, Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;-><init>(Landroid/content/Context;Landroid/view/Display;)V

    sput-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSdDriverPresentation:Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

    return-object v0
.end method

.method public static haveAbnormal()Z
    .locals 3

    .line 531
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->isShowing()Z

    move-result v0

    if-nez v0, :cond_2

    :cond_0
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSdDriverPresentation:Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

    if-eqz v0, :cond_1

    .line 532
    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;->isShowing()Z

    move-result v0

    if-nez v0, :cond_2

    :cond_1
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    if-eqz v0, :cond_5

    .line 533
    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/MapPresentation;->isShowing()Z

    move-result v0

    if-nez v0, :cond_2

    goto :goto_1

    .line 537
    :cond_2
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->detectionAbnormal(Lcom/example/helloworlddashboard/model/presentation/PresentationBase;)Z

    move-result v0

    if-nez v0, :cond_4

    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSdDriverPresentation:Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->detectionAbnormal(Lcom/example/helloworlddashboard/model/presentation/PresentationBase;)Z

    move-result v0

    if-nez v0, :cond_4

    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->detectionAbnormal(Lcom/example/helloworlddashboard/model/presentation/PresentationBase;)Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_0

    :cond_3
    const/4 v1, 0x0

    :cond_4
    :goto_0
    return v1

    :cond_5
    :goto_1
    const-string v0, "PresentationHelper"

    const-string v2, "No presentation is displayed on the meter side."

    .line 534
    invoke-static {v0, v2}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v1
.end method

.method public static isAnimShowing()Z
    .locals 3

    .line 369
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->isShowing()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    .line 370
    :goto_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "isAnimShowing result:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "PresentationHelper"

    invoke-static {v2, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v0
.end method

.method public static isFitnessAnimShowing()Z
    .locals 1

    .line 255
    invoke-static {}, Lcom/example/fitness/DashboardFitnessAPI;->getInstance()Lcom/example/fitness/DashboardFitnessAPI;

    move-result-object v0

    invoke-virtual {v0}, Lcom/example/fitness/DashboardFitnessAPI;->isFitnessShowing()Z

    move-result v0

    return v0
.end method

.method public static isMapShowing()Z
    .locals 3

    .line 358
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/MapPresentation;->isShowing()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    .line 359
    :goto_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "isMapShowing result:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "PresentationHelper"

    invoke-static {v2, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v0
.end method

.method public static isSpecialAnimShowing()Z
    .locals 7

    .line 381
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isFitnessAnimShowing()Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    const-string v0, "PresentationHelper"

    const-string v2, "isSpecialAnimShowing fitness"

    .line 382
    invoke-static {v0, v2}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v1

    .line 388
    :cond_0
    const-class v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;

    monitor-enter v0

    .line 389
    :try_start_0
    sget-object v2, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    .line 390
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const/4 v0, 0x0

    if-eqz v2, :cond_4

    .line 391
    invoke-virtual {v2}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->isShowing()Z

    move-result v3

    if-nez v3, :cond_1

    goto :goto_1

    .line 394
    :cond_1
    invoke-virtual {v2}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->getCurrentAnimationType()I

    move-result v3

    const-string v4, "PresentationHelper"

    .line 395
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "isSpecialAnimShowing currentAnimType:"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-static {v4, v5}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/16 v4, 0x14

    if-eq v3, v4, :cond_3

    const/16 v4, 0x16

    if-ne v3, v4, :cond_2

    goto :goto_0

    :cond_2
    move v1, v0

    :cond_3
    :goto_0
    move v0, v1

    :cond_4
    :goto_1
    const-string v1, "PresentationHelper"

    .line 400
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "isSpecialAnimShowing result:"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, " mBasePresentation:"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v0

    :catchall_0
    move-exception v1

    .line 390
    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v1
.end method

.method public static isSpecialAnimShowing(I)Z
    .locals 3

    .line 413
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->isShowing()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 416
    :cond_0
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->getCurrentAnimationType()I

    move-result v0

    if-ne v0, p0, :cond_1

    const/4 v0, 0x1

    move v1, v0

    .line 418
    :cond_1
    :goto_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "isSpecialAnimShowing result:"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, " , animationType:"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p0

    const-string v0, " mBasePresentation:"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mBasePresentation:Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    const-string v0, "PresentationHelper"

    invoke-static {v0, p0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v1
.end method

.method static synthetic lambda$showMapMode$6(Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V
    .locals 2

    .line 308
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/MapPresentation;->isShowing()Z

    move-result v0

    if-eqz v0, :cond_0

    const-string v0, "PresentationHelper"

    const-string v1, "mMapPresentation is show"

    .line 309
    invoke-static {v0, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 310
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSdDriverPresentation:Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;->isShowing()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 311
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSdDriverPresentation:Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;->dismissFromMap()V

    :cond_0
    if-eqz p0, :cond_1

    .line 315
    invoke-interface {p0}, Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;->onPresentationShow()V

    :cond_1
    return-void
.end method

.method static synthetic lambda$showOneDisplay$2(Lcom/example/helloworlddashboard/model/presentation/BasePresentation;)V
    .locals 1

    .line 163
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->dismiss()V

    .line 164
    sget-object p0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSdDriverPresentation:Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

    if-eqz p0, :cond_0

    .line 165
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;->dismiss()V

    :cond_0
    const-string p0, "PresentationHelper"

    const-string v0, "showOneDisplay onPlayComplete"

    .line 167
    invoke-static {p0, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method static synthetic lambda$showOneDisplay$3(Lcom/example/helloworlddashboard/model/presentation/BasePresentation;)V
    .locals 1

    .line 187
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->dismiss()V

    const-string p0, "PresentationHelper"

    const-string v0, "showOneDisplay === dismiss"

    .line 188
    invoke-static {p0, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method static synthetic lambda$showOneDisplay$4(ILandroid/content/Context;Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;Lcom/example/helloworlddashboard/model/presentation/BasePresentation;)V
    .locals 2

    .line 184
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "showOneDisplay play complete will execute driveType:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "PresentationHelper"

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 185
    invoke-static {p1, p0, p2}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showOneDisplaySanD(Landroid/content/Context;ILcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    .line 186
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object p0

    new-instance p1, Lcom/example/helloworlddashboard/utils/-$$Lambda$PresentationHelper$S1n7KgpOw1pvXy4qdnCFphyTD0c;

    invoke-direct {p1, p3}, Lcom/example/helloworlddashboard/utils/-$$Lambda$PresentationHelper$S1n7KgpOw1pvXy4qdnCFphyTD0c;-><init>(Lcom/example/helloworlddashboard/model/presentation/BasePresentation;)V

    const-wide/16 p2, 0xc8

    invoke-virtual {p0, p1, p2, p3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    return-void
.end method

.method static synthetic lambda$showPresentation$0(Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;Landroid/app/Presentation;Landroid/content/DialogInterface;)V
    .locals 0

    if-eqz p0, :cond_0

    .line 142
    invoke-interface {p0}, Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;->onPresentationShow()V

    .line 144
    :cond_0
    new-instance p0, Ljava/lang/StringBuilder;

    invoke-direct {p0}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "presentation start show. presentation:"

    invoke-virtual {p0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    const-string p1, "PresentationHelper"

    invoke-static {p1, p0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method static synthetic lambda$showPresentation$1(Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V
    .locals 0

    if-eqz p0, :cond_0

    .line 151
    invoke-interface {p0}, Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;->onPresentationShow()V

    :cond_0
    return-void
.end method

.method static synthetic lambda$showSimpleMap$5(Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V
    .locals 0

    if-eqz p0, :cond_0

    .line 282
    invoke-interface {p0}, Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;->onPresentationShow()V

    :cond_0
    return-void
.end method

.method public static showKeepOn(Landroid/content/Context;)V
    .locals 3

    .line 325
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mKeepOnPresentation:Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;

    const-string v1, "PresentationHelper"

    if-eqz v0, :cond_0

    const-string p0, "updateKeepOnMode showKeepOn fail. mKeepOnPresentation is not null."

    .line 326
    invoke-static {v1, p0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 329
    :cond_0
    new-instance v0, Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;

    const/4 v2, 0x1

    invoke-static {p0, v2}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->getDisplayByIndex(Landroid/content/Context;I)Landroid/view/Display;

    move-result-object v2

    invoke-direct {v0, p0, v2}, Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;-><init>(Landroid/content/Context;Landroid/view/Display;)V

    sput-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mKeepOnPresentation:Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;

    const/4 p0, 0x0

    .line 330
    invoke-static {v0, p0}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showPresentation(Landroid/app/Presentation;Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    const-string p0, "updateKeepOnMode showKeepOn"

    .line 331
    invoke-static {v1, p0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public static showMapMode(Landroid/content/Context;IZLcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V
    .locals 3

    const-string v0, " showMapMode"

    .line 294
    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 297
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    const-string v1, "PresentationHelper"

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/MapPresentation;->isShowing()Z

    move-result v0

    if-eqz v0, :cond_0

    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/MapPresentation;->getShowMode()I

    move-result v0

    if-ne v0, p1, :cond_0

    .line 298
    new-instance p0, Ljava/lang/StringBuilder;

    invoke-direct {p0}, Ljava/lang/StringBuilder;-><init>()V

    const-string p1, "mMapPresentation is show. will update isNightMode:"

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, p2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {v1, p0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 299
    sget-object p0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    invoke-virtual {p0, p2}, Lcom/example/helloworlddashboard/model/presentation/MapPresentation;->updateDayOrNight(Z)V

    return-void

    :cond_0
    const-string v0, "dismissSdPresentation= showMapMode"

    .line 302
    invoke-static {v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 303
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissMap()V

    .line 304
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "mMapPresentation mode="

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, " isNightMode="

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 306
    new-instance v0, Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    const/4 v1, 0x1

    invoke-static {p0, v1}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->getDisplayByIndex(Landroid/content/Context;I)Landroid/view/Display;

    move-result-object v1

    invoke-direct {v0, p0, v1, p1, p2}, Lcom/example/helloworlddashboard/model/presentation/MapPresentation;-><init>(Landroid/content/Context;Landroid/view/Display;IZ)V

    sput-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    .line 307
    new-instance p0, Lcom/example/helloworlddashboard/utils/-$$Lambda$PresentationHelper$eRDXBl81_H3zcFparAD7MAlLwBk;

    invoke-direct {p0, p3}, Lcom/example/helloworlddashboard/utils/-$$Lambda$PresentationHelper$eRDXBl81_H3zcFparAD7MAlLwBk;-><init>(Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    .line 318
    sget-object p1, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mMapPresentation:Lcom/example/helloworlddashboard/model/presentation/MapPresentation;

    invoke-static {p1, p0}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showPresentation(Landroid/app/Presentation;Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    return-void
.end method

.method public static showOneDisplay(Landroid/content/Context;Landroid/car/hardware/cabin/CarCabinManager;IILcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V
    .locals 1

    const/4 p1, 0x1

    .line 180
    invoke-static {p0, p1}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->getBasePresentation(Landroid/content/Context;I)Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 182
    invoke-static {p1, p4}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showPresentation(Landroid/app/Presentation;Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    .line 183
    new-instance v0, Lcom/example/helloworlddashboard/utils/-$$Lambda$PresentationHelper$-TQwDe7X1RO2IM5cwYlQmJ4eTYY;

    invoke-direct {v0, p3, p0, p4, p1}, Lcom/example/helloworlddashboard/utils/-$$Lambda$PresentationHelper$-TQwDe7X1RO2IM5cwYlQmJ4eTYY;-><init>(ILandroid/content/Context;Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;Lcom/example/helloworlddashboard/model/presentation/BasePresentation;)V

    invoke-virtual {p1, p2, v0}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->setInfo(ILcom/example/helloworlddashboard/model/presentation/BasePresentation$OnPlayCompleteListener;)V

    const-string p0, "PresentationHelper"

    const-string p1, "showOneDisplay context,carcabinmanager,type,driveType"

    .line 191
    invoke-static {p0, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public static showOneDisplay(Landroid/content/Context;Landroid/car/hardware/cabin/CarCabinManager;ILcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V
    .locals 0

    const/4 p1, 0x1

    .line 159
    invoke-static {p0, p1}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->getBasePresentation(Landroid/content/Context;I)Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    move-result-object p0

    if-eqz p0, :cond_0

    .line 161
    invoke-static {p0, p3}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showPresentation(Landroid/app/Presentation;Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    .line 162
    new-instance p1, Lcom/example/helloworlddashboard/utils/-$$Lambda$PresentationHelper$_FzYiR6FgFHbMzfa96_F-jXn5Ok;

    invoke-direct {p1, p0}, Lcom/example/helloworlddashboard/utils/-$$Lambda$PresentationHelper$_FzYiR6FgFHbMzfa96_F-jXn5Ok;-><init>(Lcom/example/helloworlddashboard/model/presentation/BasePresentation;)V

    invoke-virtual {p0, p2, p1}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->setInfo(ILcom/example/helloworlddashboard/model/presentation/BasePresentation$OnPlayCompleteListener;)V

    const-string p0, "PresentationHelper"

    const-string p1, "showOneDisplay context,carcabinmanager,type"

    .line 169
    invoke-static {p0, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public static showOneDisplayFitness(Landroid/content/Context;Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V
    .locals 0

    .line 245
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->isSpecialAnimShowing()Z

    move-result p1

    if-eqz p1, :cond_0

    .line 246
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissPlayAim()V

    .line 248
    :cond_0
    invoke-static {}, Lcom/example/fitness/DashboardFitnessAPI;->getInstance()Lcom/example/fitness/DashboardFitnessAPI;

    move-result-object p1

    invoke-virtual {p1, p0}, Lcom/example/fitness/DashboardFitnessAPI;->showOneDisplayFitness(Landroid/content/Context;)V

    return-void
.end method

.method public static showOneDisplayRepeatMedia(Landroid/content/Context;Landroid/car/hardware/cabin/CarCabinManager;ILcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V
    .locals 1

    const/4 v0, 0x1

    .line 201
    invoke-static {p0, v0, v0}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->getBasePresentation(Landroid/content/Context;IZ)Lcom/example/helloworlddashboard/model/presentation/BasePresentation;

    move-result-object p0

    if-eqz p0, :cond_1

    .line 203
    invoke-static {p0, p3}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showPresentation(Landroid/app/Presentation;Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    .line 204
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->getCurrentAnimationType()I

    move-result p3

    if-eq p3, p2, :cond_0

    const/4 p3, 0x0

    .line 205
    invoke-virtual {p0, p1, p2, p3}, Lcom/example/helloworlddashboard/model/presentation/BasePresentation;->setInfo(Landroid/car/hardware/cabin/CarCabinManager;ILcom/example/helloworlddashboard/model/presentation/BasePresentation$OnPlayCompleteListener;)V

    .line 207
    :cond_0
    new-instance p0, Ljava/lang/StringBuilder;

    invoke-direct {p0}, Ljava/lang/StringBuilder;-><init>()V

    const-string p1, "showOneDisplayRepeatMedia animType\uff1a"

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    const-string p1, "PresentationHelper"

    invoke-static {p1, p0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    :cond_1
    return-void
.end method

.method public static showOneDisplaySanD(Landroid/content/Context;ILcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V
    .locals 2

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 213
    invoke-static {p0, p1, v0, v1, p2}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showOneDisplaySanD(Landroid/content/Context;IZZLcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    return-void
.end method

.method public static showOneDisplaySanD(Landroid/content/Context;IZZLcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V
    .locals 1

    const/4 p3, 0x1

    .line 223
    invoke-static {p0, p3}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->getSdDriverPresentation(Landroid/content/Context;I)Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;

    move-result-object p0

    .line 224
    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "showOneDisplaySanD type:"

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p3

    const-string v0, " , isDiffTheme:"

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    const-string p3, "PresentationHelper"

    invoke-static {p3, p2}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/4 p2, 0x5

    if-ne p1, p2, :cond_1

    if-eqz p0, :cond_0

    .line 226
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;->isShowing()Z

    move-result p1

    if-eqz p1, :cond_0

    .line 227
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;->dismiss()V

    :cond_0
    return-void

    :cond_1
    const/4 p2, 0x3

    if-eq p1, p2, :cond_2

    const/4 p2, 0x6

    if-eq p1, p2, :cond_2

    .line 233
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissMap()V

    :cond_2
    if-eqz p0, :cond_3

    .line 236
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/presentation/SDDriverPresentation;->reshow()V

    .line 237
    invoke-static {p0, p4}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showPresentation(Landroid/app/Presentation;Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    :cond_3
    return-void
.end method

.method public static showPresentation(Landroid/app/Presentation;Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V
    .locals 2

    if-nez p0, :cond_0

    return-void

    .line 134
    :cond_0
    invoke-virtual {p0}, Landroid/app/Presentation;->isShowing()Z

    move-result v0

    const-string v1, "PresentationHelper"

    if-eqz v0, :cond_2

    if-eqz p1, :cond_1

    .line 136
    invoke-interface {p1}, Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;->onPresentationShow()V

    .line 138
    :cond_1
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "presentation is showing. presentation:"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {v1, p0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    .line 140
    :cond_2
    new-instance v0, Lcom/example/helloworlddashboard/utils/-$$Lambda$PresentationHelper$A6BKWHvE-5Xuy1Ze3kpDWYLrLvg;

    invoke-direct {v0, p1, p0}, Lcom/example/helloworlddashboard/utils/-$$Lambda$PresentationHelper$A6BKWHvE-5Xuy1Ze3kpDWYLrLvg;-><init>(Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;Landroid/app/Presentation;)V

    invoke-virtual {p0, v0}, Landroid/app/Presentation;->setOnShowListener(Landroid/content/DialogInterface$OnShowListener;)V

    const-string v0, "presentation show"

    .line 146
    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 148
    invoke-virtual {p0}, Landroid/app/Presentation;->show()V

    .line 149
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object p0

    new-instance v0, Lcom/example/helloworlddashboard/utils/-$$Lambda$PresentationHelper$jpXqVntd4LOlmpsAArSmSrmCcQg;

    invoke-direct {v0, p1}, Lcom/example/helloworlddashboard/utils/-$$Lambda$PresentationHelper$jpXqVntd4LOlmpsAArSmSrmCcQg;-><init>(Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    invoke-virtual {p0, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_0
    return-void
.end method

.method public static showSimpleMap(Landroid/content/Context;ZLcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V
    .locals 3

    .line 272
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSimpleMapPresentation:Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;

    const-string v1, "PresentationHelper"

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;->isShowing()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 273
    new-instance p0, Ljava/lang/StringBuilder;

    invoke-direct {p0}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "mSimpleMapPresentation is show. will update isNightMode:"

    invoke-virtual {p0, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {v1, p0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 274
    sget-object p0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSimpleMapPresentation:Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;

    invoke-virtual {p0, p1}, Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;->updateDayOrNight(Z)V

    return-void

    .line 277
    :cond_0
    invoke-static {}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->dismissSimpleMap()V

    .line 278
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "mSimpleMapPresentation  isNightMode="

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 279
    new-instance v0, Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;

    const/4 v1, 0x1

    invoke-static {p0, v1}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->getDisplayByIndex(Landroid/content/Context;I)Landroid/view/Display;

    move-result-object v1

    const/4 v2, 0x2

    invoke-direct {v0, p0, v1, v2, p1}, Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;-><init>(Landroid/content/Context;Landroid/view/Display;IZ)V

    sput-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSimpleMapPresentation:Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;

    .line 280
    new-instance p0, Lcom/example/helloworlddashboard/utils/-$$Lambda$PresentationHelper$y39ogOlbB_XUdLmcxkvm0iJWczI;

    invoke-direct {p0, p2}, Lcom/example/helloworlddashboard/utils/-$$Lambda$PresentationHelper$y39ogOlbB_XUdLmcxkvm0iJWczI;-><init>(Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    .line 285
    sget-object p1, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mSimpleMapPresentation:Lcom/example/helloworlddashboard/model/presentation/SimpleMapPresentation;

    invoke-static {p1, p0}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showPresentation(Landroid/app/Presentation;Lcom/example/helloworlddashboard/utils/PresentationHelper$OnPresentationShowCallback;)V

    return-void
.end method

.method public static updateKeepOnMode(Landroid/content/Context;Z)V
    .locals 3

    .line 340
    sget-object v0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mKeepOnPresentation:Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;

    const-string v1, "PresentationHelper"

    if-nez v0, :cond_0

    .line 341
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "updateKeepOnMode current isNight:"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 342
    invoke-static {p0}, Lcom/example/helloworlddashboard/utils/PresentationHelper;->showKeepOn(Landroid/content/Context;)V

    .line 344
    :cond_0
    sget-object p0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mKeepOnPresentation:Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;

    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;->isShowing()Z

    move-result p0

    if-nez p0, :cond_1

    .line 345
    new-instance p0, Ljava/lang/StringBuilder;

    invoke-direct {p0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "updateKeepOnMode fail. mKeepOnPresentation is not show. isNight:"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {v1, p0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 346
    sget-object p0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mKeepOnPresentation:Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;

    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;->show()V

    .line 348
    :cond_1
    new-instance p0, Ljava/lang/StringBuilder;

    invoke-direct {p0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "updateKeepOnMode update:"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p0

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {v1, p0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 349
    sget-object p0, Lcom/example/helloworlddashboard/utils/PresentationHelper;->mKeepOnPresentation:Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;

    invoke-virtual {p0, p1}, Lcom/example/helloworlddashboard/model/presentation/KeepOnPresentation;->updateNightOrDay(Z)V

    return-void
.end method
