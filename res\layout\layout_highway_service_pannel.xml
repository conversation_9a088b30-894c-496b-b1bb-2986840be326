<?xml version="1.0" encoding="utf-8"?>
<merge
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="@dimen/tp_576" android:layout_height="@dimen/tp_96">
        <TextView android:id="@id/txt_type" android:layout_width="wrap_content" android:text="@string/route_guide_highway_service" app:layout_constraintBottom_toTopOf="@id/wrapper_detail" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_chainStyle="packed" style="@style/HighwayPanelTypeText" />
        <TextView android:id="@id/txt_distance" android:layout_marginEnd="@dimen/tp_4" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/txt_unit" app:layout_constraintTop_toTopOf="parent" style="@style/HighwayPanelDistanceText" />
        <TextView android:id="@id/txt_unit" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/HighwayPanelUnitText" />
        <LinearLayout android:id="@id/wrapper_detail" app:layout_constraintBottom_toTopOf="@id/iv_tollgate" app:layout_constraintStart_toStartOf="@id/txt_type" app:layout_constraintTop_toBottomOf="@id/txt_type" style="@style/HighwayPanelIconList" />
        <ImageView android:id="@id/iv_tollgate" android:background="@mipmap/icon_sa_tollgate" android:visibility="gone" android:layout_width="@dimen/tp_32" android:layout_height="@dimen/tp_32" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="@id/txt_type" app:layout_constraintTop_toBottomOf="@id/wrapper_detail" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>