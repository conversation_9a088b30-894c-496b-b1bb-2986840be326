<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:gravity="center" android:focusable="false" android:focusableInTouchMode="false" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextureView android:id="@id/sv_main_surface" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <ImageView android:id="@id/img" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="fitXY" />
</FrameLayout>