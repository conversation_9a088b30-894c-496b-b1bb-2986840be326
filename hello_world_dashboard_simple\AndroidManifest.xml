<?xml version="1.0" encoding="utf-8" standalone="no"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" 
    android:compileSdkVersion="30" 
    android:compileSdkVersionCodename="11" 
    android:sharedUserId="android.uid.system"
    package="com.example.helloworlddashboard" 
    platformBuildVersionCode="30" 
    platformBuildVersionName="11">
    
    <!-- Dashboard permissions from original APK -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.CALL_PHONE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.car.permission.CAR_VENDOR_EXTENSION"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.REORDER_TASKS"/>
    
    <application 
        android:allowBackup="true" 
        android:extractNativeLibs="false" 
        android:icon="@android:drawable/ic_dialog_info" 
        android:label="@string/app_name" 
        android:supportsRtl="true" 
        android:theme="@android:style/Theme.Material.Light" 
        android:usesCleartextTraffic="true">
        
        <activity 
            android:exported="true" 
            android:name="com.example.helloworlddashboard.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        
    </application>
</manifest>
