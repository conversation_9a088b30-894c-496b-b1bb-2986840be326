<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item type="id" name="accessibility_action_clickable_span" />
    <item type="id" name="accessibility_custom_action_0" />
    <item type="id" name="accessibility_custom_action_1" />
    <item type="id" name="accessibility_custom_action_10" />
    <item type="id" name="accessibility_custom_action_11" />
    <item type="id" name="accessibility_custom_action_12" />
    <item type="id" name="accessibility_custom_action_13" />
    <item type="id" name="accessibility_custom_action_14" />
    <item type="id" name="accessibility_custom_action_15" />
    <item type="id" name="accessibility_custom_action_16" />
    <item type="id" name="accessibility_custom_action_17" />
    <item type="id" name="accessibility_custom_action_18" />
    <item type="id" name="accessibility_custom_action_19" />
    <item type="id" name="accessibility_custom_action_2" />
    <item type="id" name="accessibility_custom_action_20" />
    <item type="id" name="accessibility_custom_action_21" />
    <item type="id" name="accessibility_custom_action_22" />
    <item type="id" name="accessibility_custom_action_23" />
    <item type="id" name="accessibility_custom_action_24" />
    <item type="id" name="accessibility_custom_action_25" />
    <item type="id" name="accessibility_custom_action_26" />
    <item type="id" name="accessibility_custom_action_27" />
    <item type="id" name="accessibility_custom_action_28" />
    <item type="id" name="accessibility_custom_action_29" />
    <item type="id" name="accessibility_custom_action_3" />
    <item type="id" name="accessibility_custom_action_30" />
    <item type="id" name="accessibility_custom_action_31" />
    <item type="id" name="accessibility_custom_action_4" />
    <item type="id" name="accessibility_custom_action_5" />
    <item type="id" name="accessibility_custom_action_6" />
    <item type="id" name="accessibility_custom_action_7" />
    <item type="id" name="accessibility_custom_action_8" />
    <item type="id" name="accessibility_custom_action_9" />
    <item type="id" name="action_bar" />
    <item type="id" name="action_bar_activity_content" />
    <item type="id" name="action_bar_container" />
    <item type="id" name="action_bar_root" />
    <item type="id" name="action_bar_spinner" />
    <item type="id" name="action_bar_subtitle" />
    <item type="id" name="action_bar_title" />
    <item type="id" name="action_container" />
    <item type="id" name="action_context_bar" />
    <item type="id" name="action_divider" />
    <item type="id" name="action_image" />
    <item type="id" name="action_menu_divider" />
    <item type="id" name="action_menu_presenter" />
    <item type="id" name="action_mode_bar" />
    <item type="id" name="action_mode_bar_stub" />
    <item type="id" name="action_mode_close_button" />
    <item type="id" name="action_text" />
    <item type="id" name="actions" />
    <item type="id" name="activity_chooser_view_content" />
    <item type="id" name="alertTitle" />
    <item type="id" name="all_activity" />
    <item type="id" name="all_guide_content" />
    <item type="id" name="btn_azimuth" />
    <item type="id" name="btn_over_view" />
    <item type="id" name="btn_overview_return" />
    <item type="id" name="buttonPanel" />
    <item type="id" name="cancel_button" />
    <item type="id" name="checkbox" />
    <item type="id" name="checked" />
    <item type="id" name="chip" />
    <item type="id" name="chip1" />
    <item type="id" name="chip2" />
    <item type="id" name="chip3" />
    <item type="id" name="chip_group" />
    <item type="id" name="chronometer" />
    <item type="id" name="circle_center" />
    <item type="id" name="confirm_button" />
    <item type="id" name="container" />
    <item type="id" name="content" />
    <item type="id" name="contentPanel" />
    <item type="id" name="coordinator" />
    <item type="id" name="cross_eta_view" />
    <item type="id" name="customPanel" />
    <item type="id" name="dash_clear_1" />
    <item type="id" name="dash_clear_2" />
    <item type="id" name="dash_endlose" />
    <item type="id" name="dash_endwin" />
    <item type="id" name="dash_ganga" />
    <item type="id" name="dash_gongxini" />
    <item type="id" name="dash_iv" />
    <item type="id" name="dash_jiayoujiayou" />
    <item type="id" name="dash_layout" />
    <item type="id" name="dash_logob" />
    <item type="id" name="dash_m30" />
    <item type="id" name="dash_m50" />
    <item type="id" name="dash_qipao" />
    <item type="id" name="dash_qipao2" />
    <item type="id" name="dash_qiqiu1" />
    <item type="id" name="dash_qiqiu11" />
    <item type="id" name="dash_qiqiu2" />
    <item type="id" name="dash_qiqiu22" />
    <item type="id" name="dash_rect" />
    <item type="id" name="dash_sun" />
    <item type="id" name="dash_tv" />
    <item type="id" name="date_picker_actions" />
    <item type="id" name="decor_content_parent" />
    <item type="id" name="default_activity_button" />
    <item type="id" name="design_bottom_sheet" />
    <item type="id" name="design_menu_item_action_area" />
    <item type="id" name="design_menu_item_action_area_stub" />
    <item type="id" name="design_menu_item_text" />
    <item type="id" name="design_navigation_view" />
    <item type="id" name="dialog_button" />
    <item type="id" name="displaySurfaceView" />
    <item type="id" name="divider_lane_line" />
    <item type="id" name="edit_query" />
    <item type="id" name="eta_view" />
    <item type="id" name="etc_lane" />
    <item type="id" name="expand_activities_button" />
    <item type="id" name="expanded_menu" />
    <item type="id" name="fl_content" />
    <item type="id" name="fragment_container_view_tag" />
    <item type="id" name="ghost_view" />
    <item type="id" name="ghost_view_holder" />
    <item type="id" name="glide_custom_view_target_tag" />
    <item type="id" name="group_cross" />
    <item type="id" name="group_divider" />
    <item type="id" name="guide_time_info_expect_time" />
    <item type="id" name="guide_time_info_remain_dis_time" />
    <item type="id" name="guide_tip_detail" />
    <item type="id" name="guide_tip_detail_loading" />
    <item type="id" name="guide_tip_image" />
    <item type="id" name="guide_tip_image_loading" />
    <item type="id" name="guide_tip_title" />
    <item type="id" name="guide_tip_title_loading" />
    <item type="id" name="guideline" />
    <item type="id" name="header_title" />
    <item type="id" name="highway_first" />
    <item type="id" name="highway_second" />
    <item type="id" name="home" />
    <item type="id" name="icon" />
    <item type="id" name="icon_group" />
    <item type="id" name="image" />
    <item type="id" name="img" />
    <item type="id" name="img_simple_navi" />
    <item type="id" name="img_simple_navi_bg" />
    <item type="id" name="img_simple_track" />
    <item type="id" name="img_simple_track_bg" />
    <item type="id" name="info" />
    <item type="id" name="item_touch_helper_previous_elevation" />
    <item type="id" name="iv_tollgate" />
    <item type="id" name="iv_via" />
    <item type="id" name="line1" />
    <item type="id" name="line3" />
    <item type="id" name="list_item" />
    <item type="id" name="ll_highway" />
    <item type="id" name="masked" />
    <item type="id" name="material_clock_display" />
    <item type="id" name="material_clock_face" />
    <item type="id" name="material_clock_hand" />
    <item type="id" name="material_clock_period_am_button" />
    <item type="id" name="material_clock_period_pm_button" />
    <item type="id" name="material_clock_period_toggle" />
    <item type="id" name="material_hour_text_input" />
    <item type="id" name="material_hour_tv" />
    <item type="id" name="material_label" />
    <item type="id" name="material_minute_text_input" />
    <item type="id" name="material_minute_tv" />
    <item type="id" name="material_textinput_timepicker" />
    <item type="id" name="material_timepicker_cancel_button" />
    <item type="id" name="material_timepicker_container" />
    <item type="id" name="material_timepicker_edit_text" />
    <item type="id" name="material_timepicker_mode_button" />
    <item type="id" name="material_timepicker_ok_button" />
    <item type="id" name="material_timepicker_view" />
    <item type="id" name="material_value_index" />
    <item type="id" name="message" />
    <item type="id" name="month_grid" />
    <item type="id" name="month_navigation_bar" />
    <item type="id" name="month_navigation_fragment_toggle" />
    <item type="id" name="month_navigation_next" />
    <item type="id" name="month_navigation_previous" />
    <item type="id" name="month_title" />
    <item type="id" name="motion_base" />
    <item type="id" name="mtrl_anchor_parent" />
    <item type="id" name="mtrl_calendar_day_selector_frame" />
    <item type="id" name="mtrl_calendar_days_of_week" />
    <item type="id" name="mtrl_calendar_frame" />
    <item type="id" name="mtrl_calendar_main_pane" />
    <item type="id" name="mtrl_calendar_months" />
    <item type="id" name="mtrl_calendar_selection_frame" />
    <item type="id" name="mtrl_calendar_text_input_frame" />
    <item type="id" name="mtrl_calendar_year_selector_frame" />
    <item type="id" name="mtrl_card_checked_layer_id" />
    <item type="id" name="mtrl_child_content_container" />
    <item type="id" name="mtrl_internal_children_alpha_tag" />
    <item type="id" name="mtrl_motion_snapshot_view" />
    <item type="id" name="mtrl_picker_fullscreen" />
    <item type="id" name="mtrl_picker_header" />
    <item type="id" name="mtrl_picker_header_selection_text" />
    <item type="id" name="mtrl_picker_header_title_and_selection" />
    <item type="id" name="mtrl_picker_header_toggle" />
    <item type="id" name="mtrl_picker_text_input_date" />
    <item type="id" name="mtrl_picker_text_input_range_end" />
    <item type="id" name="mtrl_picker_text_input_range_start" />
    <item type="id" name="mtrl_picker_title_text" />
    <item type="id" name="mtrl_view_tag_bottom_padding" />
    <item type="id" name="navigation_bar_item_icon_view" />
    <item type="id" name="navigation_bar_item_labels_group" />
    <item type="id" name="navigation_bar_item_large_label_view" />
    <item type="id" name="navigation_bar_item_small_label_view" />
    <item type="id" name="navigation_header_container" />
    <item type="id" name="notification_background" />
    <item type="id" name="notification_main_column" />
    <item type="id" name="notification_main_column_container" />
    <item type="id" name="off" />
    <item type="id" name="on" />
    <item type="id" name="parentPanel" />
    <item type="id" name="parent_matrix" />
    <item type="id" name="progress_circular" />
    <item type="id" name="progress_horizontal" />
    <item type="id" name="radio" />
    <item type="id" name="right_control_wrapper" />
    <item type="id" name="right_icon" />
    <item type="id" name="right_side" />
    <item type="id" name="rl_simple_guide_big_distance" />
    <item type="id" name="rl_simple_navi" />
    <item type="id" name="rl_track" />
    <item type="id" name="route_guide_continue_navi_btn" />
    <item type="id" name="route_guide_exit_navi" />
    <item type="id" name="route_guide_exit_navi_btn" />
    <item type="id" name="route_guide_highSpeed_entranceExit_txt" />
    <item type="id" name="route_guide_in_out_txt" />
    <item type="id" name="route_guide_info" />
    <item type="id" name="route_guide_info_wrapper" />
    <item type="id" name="route_guide_info_wrapper_loading" />
    <item type="id" name="route_guide_lane_info" />
    <item type="id" name="route_guide_lane_top_line" />
    <item type="id" name="route_guide_next_crossing_distance" />
    <item type="id" name="route_guide_next_crossing_icon" />
    <item type="id" name="route_guide_next_crossing_wrapper" />
    <item type="id" name="route_guide_panel_view" />
    <item type="id" name="route_guide_parallel_road_info_view" />
    <item type="id" name="route_guide_preference_btn" />
    <item type="id" name="route_guide_raster_foot" />
    <item type="id" name="route_guide_raster_image_header" />
    <item type="id" name="route_guide_raster_image_header_bg" />
    <item type="id" name="route_guide_raster_image_next_crossing_distance" />
    <item type="id" name="route_guide_raster_image_next_crossing_icon" />
    <item type="id" name="route_guide_raster_image_next_crossing_wrapper" />
    <item type="id" name="route_guide_raster_image_progress_info" />
    <item type="id" name="route_guide_raster_image_progressbar_info" />
    <item type="id" name="route_guide_raster_image_road_name" />
    <item type="id" name="route_guide_raster_image_turn_dis" />
    <item type="id" name="route_guide_raster_image_turn_dis_unit" />
    <item type="id" name="route_guide_raster_image_turn_icon" />
    <item type="id" name="route_guide_raster_image_wrapper" />
    <item type="id" name="route_guide_raster_in_out_txt" />
    <item type="id" name="route_guide_raster_lane_info" />
    <item type="id" name="route_guide_raster_load" />
    <item type="id" name="route_guide_raster_next_crossing_distance_unit" />
    <item type="id" name="route_guide_road_control" />
    <item type="id" name="route_guide_road_info" />
    <item type="id" name="route_guide_road_info_detail" />
    <item type="id" name="route_guide_road_info_distance" />
    <item type="id" name="route_guide_road_info_road_name" />
    <item type="id" name="route_guide_road_info_turnIcon" />
    <item type="id" name="route_guide_road_info_turnIcon_load" />
    <item type="id" name="route_guide_road_info_turn_wrapper" />
    <item type="id" name="route_guide_road_info_voiceModeBtn" />
    <item type="id" name="route_guide_road_name" />
    <item type="id" name="route_guide_road_tip" />
    <item type="id" name="route_guide_road_tip_loading" />
    <item type="id" name="route_guide_route_info_wrapper" />
    <item type="id" name="route_guide_route_recommend_wrapper" />
    <item type="id" name="route_guide_simulate_exit_navi_btn" />
    <item type="id" name="route_guide_simulation_pause" />
    <item type="id" name="route_guide_switch_speed" />
    <item type="id" name="route_guide_time_info" />
    <item type="id" name="route_guide_tmc_bar" />
    <item type="id" name="route_guide_traffic_light_icon" />
    <item type="id" name="route_guide_traffic_light_number_text" />
    <item type="id" name="route_guide_voiceDetailedBtn" />
    <item type="id" name="route_guide_voiceMuteBtn" />
    <item type="id" name="route_guide_voiceSimpleBtn" />
    <item type="id" name="route_guide_voice_wrapper" />
    <item type="id" name="route_guide_wrapper" />
    <item type="id" name="route_guide_wrapper_loading" />
    <item type="id" name="route_guide_wrapper_road_info" />
    <item type="id" name="route_guide_zone_speed_view" />
    <item type="id" name="route_raster_lane_info" />
    <item type="id" name="row_index_key" />
    <item type="id" name="save_non_transition_alpha" />
    <item type="id" name="save_overlay_view" />
    <item type="id" name="scrollIndicatorDown" />
    <item type="id" name="scrollIndicatorUp" />
    <item type="id" name="scrollView" />
    <item type="id" name="search_badge" />
    <item type="id" name="search_bar" />
    <item type="id" name="search_button" />
    <item type="id" name="search_close_btn" />
    <item type="id" name="search_edit_frame" />
    <item type="id" name="search_go_btn" />
    <item type="id" name="search_mag_icon" />
    <item type="id" name="search_plate" />
    <item type="id" name="search_src_text" />
    <item type="id" name="search_voice_btn" />
    <item type="id" name="select_dialog_listview" />
    <item type="id" name="selection_type" />
    <item type="id" name="share_mileage" />
    <item type="id" name="share_oil_consumption" />
    <item type="id" name="shortcut" />
    <item type="id" name="simple_guide_big_arrive_tv" />
    <item type="id" name="simple_guide_big_time_plus_tv" />
    <item type="id" name="simple_guide_big_time_tv" />
    <item type="id" name="simulation_control_panel_layout" />
    <item type="id" name="snackbar_action" />
    <item type="id" name="snackbar_text" />
    <item type="id" name="spacer" />
    <item type="id" name="special_effects_controller_view_tag" />
    <item type="id" name="split_action_bar" />
    <item type="id" name="startService" />
    <item type="id" name="submenuarrow" />
    <item type="id" name="submit_area" />
    <item type="id" name="sv_main_surface" />
    <item type="id" name="tag_accessibility_actions" />
    <item type="id" name="tag_accessibility_clickable_spans" />
    <item type="id" name="tag_accessibility_heading" />
    <item type="id" name="tag_accessibility_pane_title" />
    <item type="id" name="tag_on_apply_window_listener" />
    <item type="id" name="tag_on_receive_content_listener" />
    <item type="id" name="tag_on_receive_content_mime_types" />
    <item type="id" name="tag_screen_reader_focusable" />
    <item type="id" name="tag_state_description" />
    <item type="id" name="tag_transition_group" />
    <item type="id" name="tag_unhandled_key_event_manager" />
    <item type="id" name="tag_unhandled_key_listeners" />
    <item type="id" name="tag_window_insets_animation_callback" />
    <item type="id" name="test_checkbox_android_button_tint" />
    <item type="id" name="test_checkbox_app_button_tint" />
    <item type="id" name="test_radiobutton_android_button_tint" />
    <item type="id" name="test_radiobutton_app_button_tint" />
    <item type="id" name="text" />
    <item type="id" name="text2" />
    <item type="id" name="textSpacerNoButtons" />
    <item type="id" name="textSpacerNoTitle" />
    <item type="id" name="text_input_end_icon" />
    <item type="id" name="text_input_error_icon" />
    <item type="id" name="text_input_start_icon" />
    <item type="id" name="textinput_counter" />
    <item type="id" name="textinput_error" />
    <item type="id" name="textinput_helper_text" />
    <item type="id" name="textinput_placeholder" />
    <item type="id" name="textinput_prefix_text" />
    <item type="id" name="textinput_suffix_text" />
    <item type="id" name="time" />
    <item type="id" name="title" />
    <item type="id" name="titleDividerNoCustom" />
    <item type="id" name="title_template" />
    <item type="id" name="toggle_speak" />
    <item type="id" name="toggle_tmc" />
    <item type="id" name="topPanel" />
    <item type="id" name="touch_outside" />
    <item type="id" name="touch_view" />
    <item type="id" name="transition_current_scene" />
    <item type="id" name="transition_layout_save" />
    <item type="id" name="transition_position" />
    <item type="id" name="transition_scene_layoutid_cache" />
    <item type="id" name="transition_transform" />
    <item type="id" name="tv_mark" />
    <item type="id" name="tv_max_eta_distance" />
    <item type="id" name="tv_max_eta_distance_unit" />
    <item type="id" name="tv_max_eta_point" />
    <item type="id" name="tv_max_eta_time_hour" />
    <item type="id" name="tv_max_eta_time_hour_unit" />
    <item type="id" name="tv_max_eta_time_minute" />
    <item type="id" name="tv_max_eta_time_minute_unit" />
    <item type="id" name="txt_distance" />
    <item type="id" name="txt_type" />
    <item type="id" name="txt_unit" />
    <item type="id" name="unchecked" />
    <item type="id" name="up" />
    <item type="id" name="va_eta" />
    <item type="id" name="view_car" />
    <item type="id" name="view_light_bar" />
    <item type="id" name="view_offset_helper" />
    <item type="id" name="view_speed_reminder" />
    <item type="id" name="view_speed_reminder_icon" />
    <item type="id" name="view_tmcbar_bg" />
    <item type="id" name="view_tree_lifecycle_owner" />
    <item type="id" name="view_tree_saved_state_registry_owner" />
    <item type="id" name="view_tree_view_model_store_owner" />
    <item type="id" name="visible_removing_fragment_view_tag" />
    <item type="id" name="wrapper_detail" />
    <item type="id" name="zero_corner_chip" />
</resources>
