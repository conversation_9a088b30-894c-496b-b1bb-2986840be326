<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:gravity="center">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/hello_world"
        android:textSize="24sp"
        android:layout_marginBottom="32dp"
        android:gravity="center" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/test_mcu"
        android:textSize="18sp"
        android:layout_marginBottom="24dp"
        android:gravity="center" />

    <Button
        android:id="@+id/btn_full_navi_on"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/full_navi_on"
        android:layout_marginBottom="8dp"
        android:background="#4CAF50"
        android:textColor="#FFFFFF" />

    <Button
        android:id="@+id/btn_full_navi_off"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/full_navi_off"
        android:layout_marginBottom="8dp"
        android:background="#F44336"
        android:textColor="#FFFFFF" />

    <Button
        android:id="@+id/btn_simple_navi_on"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/simple_navi_on"
        android:layout_marginBottom="8dp"
        android:background="#2196F3"
        android:textColor="#FFFFFF" />

    <Button
        android:id="@+id/btn_simple_navi_off"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/simple_navi_off"
        android:layout_marginBottom="8dp"
        android:background="#FF9800"
        android:textColor="#FFFFFF" />

</LinearLayout>
