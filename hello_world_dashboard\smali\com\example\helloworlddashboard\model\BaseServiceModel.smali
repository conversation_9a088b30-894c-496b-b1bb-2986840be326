.class public abstract Lcom/example/helloworlddashboard/model/BaseServiceModel;
.super Ljava/lang/Object;
.source "BaseServiceModel.java"

# interfaces
.implements Landroid/content/ServiceConnection;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/example/helloworlddashboard/model/BaseServiceModel$CarDeathRecipient;
    }
.end annotation


# static fields
.field private static final IGNORE_TIME:J = 0x3e8L

.field private static final TAG:Ljava/lang/String; = "BaseServiceModel"


# instance fields
.field protected cabinRegisterTime:J

.field protected mCallback:Lcom/example/helloworlddashboard/callback/CarServiceCallback;

.field protected volatile mCar:Landroid/car/Car;

.field protected final mCarCabinEventCallback:Landroid/car/hardware/cabin/CarCabinManager$CarCabinEventCallback;

.field protected volatile mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

.field protected final mCarMcuEventCallback:Landroid/car/hardware/mcu/CarMcuManager$CarMcuEventCallback;

.field protected volatile mCarMcuManager:Landroid/car/hardware/mcu/CarMcuManager;

.field protected final mContext:Landroid/content/Context;

.field protected volatile propertySets:Landroidx/collection/ArraySet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/collection/ArraySet<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/example/helloworlddashboard/callback/CarServiceCallback;)V
    .locals 1

    .line 119
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 56
    new-instance v0, Lcom/example/helloworlddashboard/model/BaseServiceModel$1;

    invoke-direct {v0, p0}, Lcom/example/helloworlddashboard/model/BaseServiceModel$1;-><init>(Lcom/example/helloworlddashboard/model/BaseServiceModel;)V

    iput-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinEventCallback:Landroid/car/hardware/cabin/CarCabinManager$CarCabinEventCallback;

    .line 87
    new-instance v0, Lcom/example/helloworlddashboard/model/BaseServiceModel$2;

    invoke-direct {v0, p0}, Lcom/example/helloworlddashboard/model/BaseServiceModel$2;-><init>(Lcom/example/helloworlddashboard/model/BaseServiceModel;)V

    iput-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarMcuEventCallback:Landroid/car/hardware/mcu/CarMcuManager$CarMcuEventCallback;

    .line 120
    iput-object p1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mContext:Landroid/content/Context;

    .line 121
    iput-object p2, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCallback:Lcom/example/helloworlddashboard/callback/CarServiceCallback;

    return-void
.end method


# virtual methods
.method public appReturnToNormal()V
    .locals 4

    const-string v0, "BaseServiceModel"

    const-string v1, "appReturnToNormal"

    .line 309
    invoke-static {v0, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 318
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    const v1, 0x2140f3ec

    const/4 v2, 0x0

    const/4 v3, 0x4

    invoke-static {v0, v1, v2, v3}, Lcom/example/helloworlddashboard/utils/DataUtil;->setCabinIntValue(Landroid/car/hardware/cabin/CarCabinManager;III)V

    .line 320
    invoke-static {}, Lcom/example/helloworlddashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v0

    new-instance v1, Lcom/example/helloworlddashboard/model/-$$Lambda$BaseServiceModel$CDk-kZdsDEtq9UT2jGstrSQYomY;

    invoke-direct {v1, p0}, Lcom/example/helloworlddashboard/model/-$$Lambda$BaseServiceModel$CDk-kZdsDEtq9UT2jGstrSQYomY;-><init>(Lcom/example/helloworlddashboard/model/BaseServiceModel;)V

    const-wide/16 v2, 0x12c

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    return-void
.end method

.method public appShutdown()V
    .locals 2

    const-string v0, "BaseServiceModel"

    const-string v1, "appShutdown"

    .line 340
    invoke-static {v0, v1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method protected enterPowerOff()V
    .locals 0

    return-void
.end method

.method protected getCarPropertyIds()[I
    .locals 1

    .line 146
    sget-object v0, Lcom/example/helloworlddashboard/utils/CarSignConstant;->CABIN_PROPERTIES:[I

    return-object v0
.end method

.method protected getMCUPropertyIds()[I
    .locals 1

    .line 154
    sget-object v0, Lcom/example/helloworlddashboard/utils/CarSignConstant;->MCU_PROPERTIES:[I

    return-object v0
.end method

.method public synthetic lambda$appReturnToNormal$0$BaseServiceModel()V
    .locals 4

    .line 321
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    const v1, 0x2140f3ec

    const/4 v2, 0x0

    const/4 v3, 0x1

    invoke-static {v0, v1, v2, v3}, Lcom/example/helloworlddashboard/utils/DataUtil;->setCabinIntValue(Landroid/car/hardware/cabin/CarCabinManager;III)V

    return-void
.end method

.method protected onChangeEvent(Landroid/car/hardware/CarPropertyValue;)V
    .locals 0

    return-void
.end method

.method protected onServiceConnected()V
    .locals 0

    .line 161
    invoke-static {}, Lcom/example/helloworlddashboard/utils/Logcat;->d()V

    return-void
.end method

.method public onServiceConnected(Landroid/content/ComponentName;Landroid/os/IBinder;)V
    .locals 2

    .line 345
    invoke-static {}, Lcom/example/helloworlddashboard/utils/Logcat;->d()V

    .line 346
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCar:Landroid/car/Car;

    if-eqz p1, :cond_1

    .line 348
    :try_start_0
    monitor-enter p0
    :try_end_0
    .catch Landroid/car/CarNotConnectedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 349
    :try_start_1
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/BaseServiceModel;->getCarPropertyIds()[I

    move-result-object p1

    invoke-static {p1}, Lcom/example/helloworlddashboard/utils/DataUtil;->intArrayToArraySets([I)Landroidx/collection/ArraySet;

    move-result-object p1

    iput-object p1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->propertySets:Landroidx/collection/ArraySet;

    .line 350
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 351
    :try_start_2
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCar:Landroid/car/Car;

    const-string v0, "cabin"

    invoke-virtual {p1, v0}, Landroid/car/Car;->getCarManager(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/car/hardware/cabin/CarCabinManager;

    iput-object p1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    .line 352
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    iget-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinEventCallback:Landroid/car/hardware/cabin/CarCabinManager$CarCabinEventCallback;

    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/BaseServiceModel;->getCarPropertyIds()[I

    move-result-object v1

    invoke-virtual {p1, v0, v1}, Landroid/car/hardware/cabin/CarCabinManager;->registerCallback(Landroid/car/hardware/cabin/CarCabinManager$CarCabinEventCallback;[I)V

    .line 353
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->cabinRegisterTime:J

    .line 354
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCar:Landroid/car/Car;

    const-string v0, "mcu_service"

    invoke-virtual {p1, v0}, Landroid/car/Car;->getCarManager(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/car/hardware/mcu/CarMcuManager;

    iput-object p1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarMcuManager:Landroid/car/hardware/mcu/CarMcuManager;

    .line 355
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarMcuManager:Landroid/car/hardware/mcu/CarMcuManager;

    if-eqz p1, :cond_0

    .line 356
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarMcuManager:Landroid/car/hardware/mcu/CarMcuManager;

    iget-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarMcuEventCallback:Landroid/car/hardware/mcu/CarMcuManager$CarMcuEventCallback;

    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/BaseServiceModel;->getMCUPropertyIds()[I

    move-result-object v1

    invoke-virtual {p1, v0, v1}, Landroid/car/hardware/mcu/CarMcuManager;->registerCallback(Landroid/car/hardware/mcu/CarMcuManager$CarMcuEventCallback;[I)V

    goto :goto_0

    :cond_0
    const-string p1, "mCarMcuManager == null"

    .line 358
    invoke-static {p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 360
    :goto_0
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCar:Landroid/car/Car;

    const-string v0, "hvac"

    invoke-virtual {p1, v0}, Landroid/car/Car;->getCarManager(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/car/hardware/hvac/CarHvacManager;

    .line 361
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/BaseServiceModel;->onServiceConnected()V

    .line 362
    iget-object p1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCallback:Lcom/example/helloworlddashboard/callback/CarServiceCallback;

    if-eqz p1, :cond_1

    .line 363
    invoke-interface {p1}, Lcom/example/helloworlddashboard/callback/CarServiceCallback;->onCarServiceConnected()V
    :try_end_2
    .catch Landroid/car/CarNotConnectedException; {:try_start_2 .. :try_end_2} :catch_0

    goto :goto_1

    :catchall_0
    move-exception p1

    .line 350
    :try_start_3
    monitor-exit p0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    :try_start_4
    throw p1
    :try_end_4
    .catch Landroid/car/CarNotConnectedException; {:try_start_4 .. :try_end_4} :catch_0

    :catch_0
    move-exception p1

    .line 366
    invoke-virtual {p1}, Landroid/car/CarNotConnectedException;->printStackTrace()V

    .line 370
    :cond_1
    :goto_1
    :try_start_5
    new-instance p1, Lcom/example/helloworlddashboard/model/BaseServiceModel$CarDeathRecipient;

    const/4 v0, 0x0

    invoke-direct {p1, p0, p2, v0}, Lcom/example/helloworlddashboard/model/BaseServiceModel$CarDeathRecipient;-><init>(Lcom/example/helloworlddashboard/model/BaseServiceModel;Landroid/os/IBinder;Lcom/example/helloworlddashboard/model/BaseServiceModel$1;)V

    const/4 v0, 0x0

    invoke-interface {p2, p1, v0}, Landroid/os/IBinder;->linkToDeath(Landroid/os/IBinder$DeathRecipient;I)V
    :try_end_5
    .catch Landroid/os/RemoteException; {:try_start_5 .. :try_end_5} :catch_1

    goto :goto_2

    :catch_1
    move-exception p1

    .line 372
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    :goto_2
    return-void
.end method

.method protected onServiceDisconnected()V
    .locals 2

    .line 168
    invoke-static {}, Lcom/example/helloworlddashboard/utils/Logcat;->d()V

    const-wide/16 v0, 0x0

    .line 169
    iput-wide v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->cabinRegisterTime:J

    return-void
.end method

.method public onServiceDisconnected(Landroid/content/ComponentName;)V
    .locals 2

    .line 378
    invoke-static {}, Lcom/example/helloworlddashboard/utils/Logcat;->w()V

    .line 379
    monitor-enter p0

    const/4 v0, 0x0

    .line 380
    :try_start_0
    iput-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    .line 381
    iput-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarMcuManager:Landroid/car/hardware/mcu/CarMcuManager;

    const-wide/16 v0, 0x0

    .line 382
    iput-wide v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->cabinRegisterTime:J

    .line 383
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 384
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/BaseServiceModel;->onServiceDisconnected()V

    if-nez p1, :cond_0

    .line 387
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/BaseServiceModel;->unregister()V

    .line 388
    invoke-virtual {p0}, Lcom/example/helloworlddashboard/model/BaseServiceModel;->register()V

    :cond_0
    return-void

    :catchall_0
    move-exception p1

    .line 383
    :try_start_1
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method public register()V
    .locals 1

    .line 173
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCar:Landroid/car/Car;

    if-nez v0, :cond_0

    .line 174
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mContext:Landroid/content/Context;

    invoke-static {v0, p0}, Landroid/car/Car;->createCar(Landroid/content/Context;Landroid/content/ServiceConnection;)Landroid/car/Car;

    move-result-object v0

    iput-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCar:Landroid/car/Car;

    .line 175
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCar:Landroid/car/Car;

    invoke-virtual {v0}, Landroid/car/Car;->connect()V

    :cond_0
    return-void
.end method

.method public requestFitnessAnimChange(IZZZZIIIIIILjava/lang/String;Ljava/lang/String;)Z
    .locals 0

    .line 332
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    const-string p3, "requestFitnessAnim cmdType:"

    invoke-virtual {p2, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p2

    invoke-virtual {p2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string p2, "BaseServiceModel"

    invoke-static {p2, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/4 p1, 0x0

    return p1
.end method

.method public sendArtistInfo([B)V
    .locals 0

    return-void
.end method

.method public sendMediaInfo([B[B)V
    .locals 0

    return-void
.end method

.method public sendNaviInfo([I)V
    .locals 3

    .line 276
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    if-eqz v0, :cond_0

    .line 277
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    const v1, 0x2141f080

    const/4 v2, 0x0

    invoke-static {v0, v1, v2, p1}, Lcom/example/helloworlddashboard/utils/DataUtil;->setCabinIntArrayValue(Landroid/car/hardware/cabin/CarCabinManager;II[I)V

    goto :goto_0

    :cond_0
    const-string p1, "BaseServiceModel"

    const-string v0, "sendNaviInfo fail. mCarCabinManager is null. "

    .line 279
    invoke-static {p1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    :goto_0
    return-void
.end method

.method public sendNextRoadName([B)V
    .locals 3

    .line 290
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    const-string v1, "BaseServiceModel"

    if-eqz v0, :cond_0

    .line 291
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "sendRoadName roadName="

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-static {p1}, Ljava/util/Arrays;->toString([B)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    if-eqz p1, :cond_1

    .line 294
    :try_start_0
    iget-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    const v1, 0x2170f081

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2, p1}, Landroid/car/hardware/cabin/CarCabinManager;->setByteArrayProperty(II[B)V
    :try_end_0
    .catch Landroid/car/CarNotConnectedException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 298
    invoke-virtual {p1}, Landroid/car/CarNotConnectedException;->printStackTrace()V

    goto :goto_0

    :cond_0
    const-string p1, "sendRoadName fail. mCarCabinManager is null."

    .line 301
    invoke-static {v1, p1}, Lcom/example/helloworlddashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    :cond_1
    :goto_0
    return-void
.end method

.method public unregister()V
    .locals 3

    const/4 v0, 0x0

    .line 180
    iput-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCallback:Lcom/example/helloworlddashboard/callback/CarServiceCallback;

    .line 181
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    if-eqz v1, :cond_0

    .line 183
    :try_start_0
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;

    iget-object v2, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinEventCallback:Landroid/car/hardware/cabin/CarCabinManager$CarCabinEventCallback;

    invoke-virtual {v1, v2}, Landroid/car/hardware/cabin/CarCabinManager;->unregisterCallback(Landroid/car/hardware/cabin/CarCabinManager$CarCabinEventCallback;)V

    .line 184
    iput-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarCabinManager:Landroid/car/hardware/cabin/CarCabinManager;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v1

    .line 186
    invoke-virtual {v1}, Ljava/lang/Exception;->printStackTrace()V

    .line 189
    :cond_0
    :goto_0
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarMcuManager:Landroid/car/hardware/mcu/CarMcuManager;

    if-eqz v1, :cond_1

    .line 190
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarMcuManager:Landroid/car/hardware/mcu/CarMcuManager;

    iget-object v2, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarMcuEventCallback:Landroid/car/hardware/mcu/CarMcuManager$CarMcuEventCallback;

    invoke-virtual {v1, v2}, Landroid/car/hardware/mcu/CarMcuManager;->unregisterCallback(Landroid/car/hardware/mcu/CarMcuManager$CarMcuEventCallback;)V

    .line 191
    iput-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCarMcuManager:Landroid/car/hardware/mcu/CarMcuManager;

    .line 193
    :cond_1
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCar:Landroid/car/Car;

    if-eqz v1, :cond_2

    .line 194
    iget-object v1, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCar:Landroid/car/Car;

    invoke-virtual {v1}, Landroid/car/Car;->disconnect()V

    .line 195
    iput-object v0, p0, Lcom/example/helloworlddashboard/model/BaseServiceModel;->mCar:Landroid/car/Car;

    .line 197
    :cond_2
    invoke-static {}, Ljava/lang/System;->gc()V

    return-void
.end method
