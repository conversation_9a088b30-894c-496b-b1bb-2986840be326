.class public interface abstract Lcom/example/helloworlddashboard/IDashboardInterface;
.super Ljava/lang/Object;
.source "IDashboardInterface.java"

# interfaces
.implements Landroid/os/IInterface;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/example/helloworlddashboard/IDashboardInterface$Stub;,
        Lcom/example/helloworlddashboard/IDashboardInterface$Default;
    }
.end annotation


# virtual methods
.method public abstract onReceiveMediaSource(Ljava/lang/String;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroid/os/RemoteException;
        }
    .end annotation
.end method

.method public abstract onReceivedMediaInfo(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;II)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroid/os/RemoteException;
        }
    .end annotation
.end method

.method public abstract requestFitnessAnim(IZZZZIIIIIILjava/lang/String;Ljava/lang/String;)Z
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroid/os/RemoteException;
        }
    .end annotation
.end method
